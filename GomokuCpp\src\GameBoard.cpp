/**
 * @file GameBoard.cpp
 * @brief GameBoard类的实现文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件实现了GameBoard类的所有方法。
 * GameBoard是五子棋游戏的核心类，负责游戏逻辑的处理。
 */

#include "GameBoard.h"
#include <sstream>
#include <iomanip>

/**
 * @brief 构造函数实现
 * 
 * 初始化一个15x15的空棋盘。
 * 使用vector的resize方法创建二维数组，并初始化为EMPTY。
 */
GameBoard::GameBoard() 
    : currentPlayer_(PieceType::BLACK), totalMoves_(0), lastMove_(0, 0) {
    
    // 初始化棋盘为15x15的二维vector，所有位置都是EMPTY
    board_.resize(BOARD_SIZE);
    for (int i = 0; i < BOARD_SIZE; ++i) {
        board_[i].resize(BOARD_SIZE, PieceType::EMPTY);
    }
}

/**
 * @brief 析构函数实现
 * 
 * 由于使用了vector，内存会自动释放，
 * 但保留析构函数是好的编程习惯。
 */
GameBoard::~GameBoard() {
    // vector会自动清理内存，这里不需要手动操作
}

/**
 * @brief 重置游戏板
 * 
 * 将所有游戏状态重置为初始值：
 * - 清空棋盘
 * - 黑子先行
 * - 步数归零
 */
void GameBoard::reset() {
    // 清空棋盘 - 将所有位置设为EMPTY
    for (int i = 0; i < BOARD_SIZE; ++i) {
        for (int j = 0; j < BOARD_SIZE; ++j) {
            board_[i][j] = PieceType::EMPTY;
        }
    }
    
    // 重置游戏状态
    currentPlayer_ = PieceType::BLACK;  // 黑子先行
    totalMoves_ = 0;                    // 步数归零
    lastMove_ = Point(0, 0);            // 重置最后一步
}

/**
 * @brief 下棋方法实现
 * 
 * @param row 行坐标
 * @param col 列坐标
 * @return 下棋是否成功
 * 
 * 这是游戏的核心方法之一。它检查下棋是否合法，
 * 如果合法就在指定位置放置棋子并切换玩家。
 */
bool GameBoard::makeMove(int row, int col) {
    // 检查坐标是否有效
    if (row < 0 || row >= BOARD_SIZE || col < 0 || col >= BOARD_SIZE) {
        return false;  // 坐标超出范围
    }
    
    // 检查该位置是否已有棋子
    if (board_[row][col] != PieceType::EMPTY) {
        return false;  // 位置已被占用
    }
    
    // 检查游戏是否已结束
    if (checkGameResult() != GameResult::CONTINUE) {
        return false;  // 游戏已结束
    }
    
    // 下棋：在指定位置放置当前玩家的棋子
    board_[row][col] = currentPlayer_;
    
    // 更新游戏状态
    lastMove_ = Point(row, col, currentPlayer_);  // 记录最后一步
    totalMoves_++;                                // 增加步数
    
    // 切换玩家
    currentPlayer_ = (currentPlayer_ == PieceType::BLACK) ? 
                     PieceType::WHITE : PieceType::BLACK;
    
    return true;  // 下棋成功
}

/**
 * @brief 下棋方法的Point版本
 * 
 * @param point 要下棋的位置
 * @return 下棋是否成功
 * 
 * 这是makeMove方法的重载版本，接受Point对象作为参数。
 * 内部调用基本的makeMove方法。
 */
bool GameBoard::makeMove(const Point& point) {
    return makeMove(point.getRow(), point.getCol());
}

/**
 * @brief 检查游戏结果
 * 
 * @return 返回当前游戏状态
 * 
 * 这个方法检查游戏是否结束，以及结束的原因：
 * 1. 检查最后一步是否形成五子连珠
 * 2. 检查是否平局（棋盘满了但没有获胜者）
 * 3. 否则游戏继续
 */
GameResult GameBoard::checkGameResult() const {
    // 如果还没有下棋，游戏继续
    if (totalMoves_ == 0) {
        return GameResult::CONTINUE;
    }
    
    // 检查最后一步是否形成五子连珠
    // 只需要检查最后下的棋子，因为只有它可能形成新的连珠
    if (checkHorizontal(lastMove_) || checkVertical(lastMove_) ||
        checkMainDiagonal(lastMove_) || checkAntiDiagonal(lastMove_)) {
        
        // 根据最后一步的棋子类型确定获胜者
        if (lastMove_.getPiece() == PieceType::BLACK) {
            return GameResult::BLACK_WIN;
        } else {
            return GameResult::WHITE_WIN;
        }
    }
    
    // 检查是否平局（棋盘满了）
    if (isFull()) {
        return GameResult::DRAW;
    }
    
    // 游戏继续
    return GameResult::CONTINUE;
}

/**
 * @brief 检查水平方向的五子连珠
 * 
 * @param point 要检查的位置
 * @return 如果有五子连珠返回true
 * 
 * 算法思路：
 * 1. 从当前位置向左数连续相同的棋子
 * 2. 从当前位置向右数连续相同的棋子
 * 3. 总数（包括当前位置）≥5则获胜
 */
bool GameBoard::checkHorizontal(const Point& point) const {
    int count = 1;  // 包括当前位置
    PieceType piece = point.getPiece();
    int row = point.getRow();
    int col = point.getCol();
    
    // 向左检查
    for (int c = col - 1; c >= 0 && board_[row][c] == piece; --c) {
        count++;
    }
    
    // 向右检查
    for (int c = col + 1; c < BOARD_SIZE && board_[row][c] == piece; ++c) {
        count++;
    }
    
    return count >= 5;  // 五子连珠
}

/**
 * @brief 检查垂直方向的五子连珠
 * 
 * @param point 要检查的位置
 * @return 如果有五子连珠返回true
 * 
 * 算法与水平检查类似，但是在垂直方向上计算。
 */
bool GameBoard::checkVertical(const Point& point) const {
    int count = 1;  // 包括当前位置
    PieceType piece = point.getPiece();
    int row = point.getRow();
    int col = point.getCol();
    
    // 向上检查
    for (int r = row - 1; r >= 0 && board_[r][col] == piece; --r) {
        count++;
    }
    
    // 向下检查
    for (int r = row + 1; r < BOARD_SIZE && board_[r][col] == piece; ++r) {
        count++;
    }
    
    return count >= 5;
}

/**
 * @brief 检查主对角线方向的五子连珠
 * 
 * @param point 要检查的位置
 * @return 如果有五子连珠返回true
 * 
 * 主对角线：从左上到右下的方向（\）
 */
bool GameBoard::checkMainDiagonal(const Point& point) const {
    int count = 1;
    PieceType piece = point.getPiece();
    int row = point.getRow();
    int col = point.getCol();
    
    // 向左上检查
    for (int r = row - 1, c = col - 1; 
         r >= 0 && c >= 0 && board_[r][c] == piece; 
         --r, --c) {
        count++;
    }
    
    // 向右下检查
    for (int r = row + 1, c = col + 1; 
         r < BOARD_SIZE && c < BOARD_SIZE && board_[r][c] == piece; 
         ++r, ++c) {
        count++;
    }
    
    return count >= 5;
}

/**
 * @brief 检查副对角线方向的五子连珠
 * 
 * @param point 要检查的位置
 * @return 如果有五子连珠返回true
 * 
 * 副对角线：从右上到左下的方向（/）
 */
bool GameBoard::checkAntiDiagonal(const Point& point) const {
    int count = 1;
    PieceType piece = point.getPiece();
    int row = point.getRow();
    int col = point.getCol();
    
    // 向右上检查
    for (int r = row - 1, c = col + 1; 
         r >= 0 && c < BOARD_SIZE && board_[r][c] == piece; 
         --r, ++c) {
        count++;
    }
    
    // 向左下检查
    for (int r = row + 1, c = col - 1; 
         r < BOARD_SIZE && c >= 0 && board_[r][c] == piece; 
         ++r, --c) {
        count++;
    }
    
    return count >= 5;
}

/**
 * @brief 获取指定位置的棋子类型
 *
 * @param row 行坐标
 * @param col 列坐标
 * @return 返回该位置的棋子类型
 *
 * 这是一个简单的getter方法，用于查询棋盘状态。
 * 包含边界检查以确保程序安全性。
 */
PieceType GameBoard::getPiece(int row, int col) const {
    // 边界检查
    if (row < 0 || row >= BOARD_SIZE || col < 0 || col >= BOARD_SIZE) {
        return PieceType::EMPTY;  // 超出边界返回空
    }
    return board_[row][col];
}

/**
 * @brief 获取当前玩家
 *
 * @return 返回当前应该下棋的玩家
 */
PieceType GameBoard::getCurrentPlayer() const {
    return currentPlayer_;
}

/**
 * @brief 获取总步数
 *
 * @return 返回已下棋子的总数
 */
int GameBoard::getTotalMoves() const {
    return totalMoves_;
}

/**
 * @brief 获取最后一步棋的位置
 *
 * @return 返回最后下棋的位置
 */
Point GameBoard::getLastMove() const {
    return lastMove_;
}

/**
 * @brief 检查棋盘是否已满
 *
 * @return 如果棋盘已满返回true
 *
 * 当总步数等于棋盘总格子数时，棋盘就满了。
 * 15x15的棋盘总共有225个位置。
 */
bool GameBoard::isFull() const {
    return totalMoves_ >= (BOARD_SIZE * BOARD_SIZE);
}

/**
 * @brief 检查指定位置是否可以下棋
 *
 * @param row 行坐标
 * @param col 列坐标
 * @return 如果可以下棋返回true
 *
 * 下棋的条件：
 * 1. 坐标在有效范围内
 * 2. 该位置为空
 * 3. 游戏还在进行中
 */
bool GameBoard::isValidMove(int row, int col) const {
    // 检查坐标范围
    if (row < 0 || row >= BOARD_SIZE || col < 0 || col >= BOARD_SIZE) {
        return false;
    }

    // 检查位置是否为空
    if (board_[row][col] != PieceType::EMPTY) {
        return false;
    }

    // 检查游戏是否还在进行
    if (checkGameResult() != GameResult::CONTINUE) {
        return false;
    }

    return true;
}

/**
 * @brief 获取棋盘的字符串表示
 *
 * @return 返回棋盘的字符串表示
 *
 * 这个方法将棋盘转换为可读的字符串格式，
 * 用于控制台显示或调试。
 *
 * 显示格式：
 * - 使用坐标标记行列
 * - ● 表示黑子
 * - ○ 表示白子
 * - + 表示空位置
 */
std::string GameBoard::toString() const {
    std::ostringstream oss;

    // 打印列标题 (A-O)
    oss << "   ";
    for (int col = 0; col < BOARD_SIZE; ++col) {
        oss << " " << static_cast<char>('A' + col);
    }
    oss << "\n";

    // 打印棋盘内容
    for (int row = 0; row < BOARD_SIZE; ++row) {
        // 打印行号 (0-14，格式化为两位数)
        oss << std::setw(2) << row << " ";

        // 打印该行的棋子
        for (int col = 0; col < BOARD_SIZE; ++col) {
            switch (board_[row][col]) {
                case PieceType::BLACK:
                    oss << " ●";  // 黑子
                    break;
                case PieceType::WHITE:
                    oss << " ○";  // 白子
                    break;
                case PieceType::EMPTY:
                default:
                    oss << " +";  // 空位置
                    break;
            }
        }
        oss << "\n";
    }

    return oss.str();
}

/**
 * @brief 获取棋盘大小
 *
 * @return 返回棋盘大小（15）
 *
 * 这是一个静态方法，可以在不创建对象的情况下调用。
 */
int GameBoard::getBoardSize() {
    return BOARD_SIZE;
}
