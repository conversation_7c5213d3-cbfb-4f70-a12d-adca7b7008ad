@echo off
REM 五子棋游戏启动脚本
REM 这个脚本用于快速启动五子棋游戏

title 五子棋游戏启动器

echo ========================================
echo 五子棋游戏 - C++版本
echo ========================================
echo.

REM 检查可执行文件是否存在
if exist "bin\Win32\Release\GomokuGame.exe" (
    echo 找到游戏程序: bin\Win32\Release\GomokuGame.exe
    echo 正在启动游戏...
    echo.
    start "" "bin\Win32\Release\GomokuGame.exe"
    echo 游戏已启动！
    echo.
    echo 如果游戏没有正常启动，请检查：
    echo 1. 是否已正确编译项目
    echo 2. 系统是否支持Win32应用程序
    echo 3. 是否缺少必要的运行库
    echo.
    pause
    exit /b 0
)

if exist "bin\x64\Release\GomokuGame.exe" (
    echo 找到游戏程序: bin\x64\Release\GomokuGame.exe
    echo 正在启动游戏...
    echo.
    start "" "bin\x64\Release\GomokuGame.exe"
    echo 游戏已启动！
    echo.
    pause
    exit /b 0
)

if exist "bin\Win32\Debug\GomokuGame.exe" (
    echo 找到游戏程序: bin\Win32\Debug\GomokuGame.exe
    echo 正在启动游戏...
    echo.
    start "" "bin\Win32\Debug\GomokuGame.exe"
    echo 游戏已启动！
    echo.
    pause
    exit /b 0
)

if exist "bin\x64\Debug\GomokuGame.exe" (
    echo 找到游戏程序: bin\x64\Debug\GomokuGame.exe
    echo 正在启动游戏...
    echo.
    start "" "bin\x64\Debug\GomokuGame.exe"
    echo 游戏已启动！
    echo.
    pause
    exit /b 0
)

REM 如果没有找到可执行文件
echo 错误: 找不到游戏程序！
echo.
echo 请先编译项目：
echo.
echo 方法1: 使用Visual Studio
echo   1. 双击 GomokuCpp.sln 打开项目
echo   2. 选择 Release 配置
echo   3. 按 F5 或 Ctrl+F5 编译运行
echo.
echo 方法2: 使用编译脚本
echo   1. 双击 build.bat 文件
echo   2. 等待编译完成
echo   3. 再次运行此脚本
echo.
echo 方法3: 使用CMake
echo   1. 打开命令提示符
echo   2. 执行: mkdir build ^&^& cd build
echo   3. 执行: cmake .. -G "Visual Studio 16 2019"
echo   4. 执行: cmake --build . --config Release
echo.
pause
exit /b 1
