/**
 * @file RankManager.h
 * @brief 五子棋游戏排行榜管理类的头文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件定义了RankManager类，用于管理五子棋游戏的排行榜功能。
 * 排行榜记录玩家的获胜信息，按照获胜所用步数排序。
 * 
 * 主要功能：
 * - 添加新的获胜记录
 * - 从文件加载排行榜数据
 * - 保存排行榜数据到文件
 * - 显示排行榜
 * - 判断成绩是否能进入排行榜
 */

#ifndef RANKMANAGER_H
#define RANKMANAGER_H

#include <string>
#include <vector>
#include <fstream>

/**
 * @brief 排行榜记录结构体
 * 
 * 存储单个玩家的获胜记录信息。
 * 使用struct而不是class，因为这只是简单的数据容器。
 */
struct RankRecord {
    std::string playerName;  // 玩家姓名
    int steps;              // 获胜所用步数
    std::string date;       // 获胜日期
    
    /**
     * @brief 默认构造函数
     */
    RankRecord();
    
    /**
     * @brief 带参数的构造函数
     * @param name 玩家姓名
     * @param stepCount 获胜步数
     * @param gameDate 游戏日期
     */
    RankRecord(const std::string& name, int stepCount, const std::string& gameDate);
    
    /**
     * @brief 比较运算符重载（用于排序）
     * @param other 要比较的记录
     * @return 如果当前记录的步数更少返回true
     * 
     * 排行榜按步数升序排列，步数越少排名越高。
     */
    bool operator<(const RankRecord& other) const;
};

/**
 * @brief 排行榜管理类
 * 
 * 这个类负责管理五子棋游戏的排行榜系统。
 * 
 * 设计特点：
 * 1. 使用vector存储排行榜记录，支持动态大小
 * 2. 自动排序功能，保持排行榜有序
 * 3. 文件持久化存储，程序重启后数据不丢失
 * 4. 限制排行榜大小，避免文件过大
 * 
 * 使用示例：
 * RankManager rankMgr;
 * rankMgr.loadFromFile("ranklist.txt");
 * rankMgr.addRecord("张三", 25);
 * rankMgr.saveToFile("ranklist.txt");
 */
class RankManager {
private:
    std::vector<RankRecord> records_;  // 排行榜记录列表
    static const int MAX_RECORDS = 10; // 最大记录数量
    std::string filename_;             // 数据文件名
    
    /**
     * @brief 对排行榜记录进行排序
     * 
     * 按照步数升序排列，步数相同时按姓名字典序排列。
     * 这是一个私有方法，在添加新记录后自动调用。
     */
    void sortRecords();
    
    /**
     * @brief 获取当前日期字符串
     * @return 返回格式化的日期字符串 (YYYY-MM-DD)
     */
    std::string getCurrentDate() const;

public:
    /**
     * @brief 默认构造函数
     * 
     * 创建一个空的排行榜管理器。
     */
    RankManager();
    
    /**
     * @brief 带文件名的构造函数
     * @param filename 排行榜数据文件名
     */
    explicit RankManager(const std::string& filename);
    
    /**
     * @brief 析构函数
     * 
     * 确保程序结束时数据被保存。
     */
    ~RankManager();
    
    /**
     * @brief 添加新的排行榜记录
     * @param playerName 玩家姓名
     * @param steps 获胜步数
     * @return 如果成功添加返回true
     * 
     * 添加逻辑：
     * 1. 创建新记录
     * 2. 插入到记录列表
     * 3. 重新排序
     * 4. 如果超过最大记录数，删除最后的记录
     */
    bool addRecord(const std::string& playerName, int steps);
    
    /**
     * @brief 判断指定步数是否能进入排行榜
     * @param steps 要检查的步数
     * @return 如果能进入排行榜返回true
     * 
     * 判断条件：
     * 1. 排行榜未满（记录数 < MAX_RECORDS）
     * 2. 或者步数比最后一名更少
     */
    bool canEnterRank(int steps) const;
    
    /**
     * @brief 获取指定步数在排行榜中的排名
     * @param steps 要查询的步数
     * @return 返回排名（1-based），如果不能进入返回-1
     */
    int getRankPosition(int steps) const;
    
    /**
     * @brief 从文件加载排行榜数据
     * @param filename 数据文件名
     * @return 如果加载成功返回true
     * 
     * 文件格式：每行一个记录，格式为 "姓名 步数 日期"
     */
    bool loadFromFile(const std::string& filename);
    
    /**
     * @brief 保存排行榜数据到文件
     * @param filename 数据文件名
     * @return 如果保存成功返回true
     */
    bool saveToFile(const std::string& filename);
    
    /**
     * @brief 使用默认文件名加载数据
     * @return 如果加载成功返回true
     */
    bool loadFromFile();
    
    /**
     * @brief 使用默认文件名保存数据
     * @return 如果保存成功返回true
     */
    bool saveToFile();
    
    /**
     * @brief 获取排行榜记录数量
     * @return 返回当前记录数量
     */
    int getRecordCount() const;
    
    /**
     * @brief 获取指定位置的记录
     * @param index 记录索引 (0-based)
     * @return 返回记录的引用，如果索引无效抛出异常
     */
    const RankRecord& getRecord(int index) const;
    
    /**
     * @brief 获取所有记录
     * @return 返回记录列表的常量引用
     */
    const std::vector<RankRecord>& getAllRecords() const;
    
    /**
     * @brief 清空所有记录
     */
    void clearRecords();
    
    /**
     * @brief 获取排行榜的字符串表示
     * @return 返回格式化的排行榜字符串
     * 
     * 输出格式：
     * 排名  姓名    步数  日期
     * 1     张三    25    2024-01-01
     * 2     李四    28    2024-01-02
     */
    std::string toString() const;
    
    /**
     * @brief 设置数据文件名
     * @param filename 新的文件名
     */
    void setFilename(const std::string& filename);
    
    /**
     * @brief 获取当前数据文件名
     * @return 返回当前文件名
     */
    std::string getFilename() const;
};

#endif // RANKMANAGER_H
