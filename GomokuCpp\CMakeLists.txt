# CMakeLists.txt for Gomoku Game C++ Version
# 五子棋游戏C++版本的CMake构建文件
# 
# 这个文件定义了如何编译五子棋游戏项目
# 适用于Visual Studio 2019及更高版本

# 设置CMake最低版本要求
cmake_minimum_required(VERSION 3.16)

# 定义项目名称和版本
project(GomokuCpp 
    VERSION 1.0.0
    DESCRIPTION "Gomoku Game implemented in C++ with Win32 GUI"
    LANGUAGES CXX
)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置编译器特定选项
if(MSVC)
    # Visual Studio编译器选项
    add_compile_options(
        /W4          # 警告级别4
        /WX          # 将警告视为错误
        /utf-8       # 使用UTF-8编码
        /permissive- # 严格标准符合性
    )
    
    # 设置Windows子系统
    set(CMAKE_WIN32_EXECUTABLE TRUE)
    
    # 添加预处理器定义
    add_compile_definitions(
        WIN32
        _WINDOWS
        UNICODE
        _UNICODE
        _CRT_SECURE_NO_WARNINGS
    )
endif()

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 定义源文件
set(SOURCES
    src/main.cpp
    src/GomokuApp.cpp
    src/GameController.cpp
    src/GameBoard.cpp
    src/RankManager.cpp
    src/Point.cpp
)

# 定义头文件
set(HEADERS
    src/GomokuApp.h
    src/GameController.h
    src/GameBoard.h
    src/RankManager.h
    src/Point.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} WIN32 ${SOURCES} ${HEADERS})

# 设置包含目录
target_include_directories(${PROJECT_NAME} PRIVATE src)

# 链接Windows库
if(WIN32)
    target_link_libraries(${PROJECT_NAME}
        user32
        gdi32
        comctl32
        kernel32
        shell32
        ole32
        oleaut32
        uuid
        comdlg32
        advapi32
    )
endif()

# 设置可执行文件属性
set_target_properties(${PROJECT_NAME} PROPERTIES
    OUTPUT_NAME "GomokuGame"
    DEBUG_POSTFIX "_d"
)

# 复制资源文件到输出目录（如果有的话）
# file(COPY resources/ DESTINATION ${CMAKE_RUNTIME_OUTPUT_DIRECTORY})

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# 创建Visual Studio项目的过滤器
if(MSVC)
    # 为源文件创建过滤器
    source_group("Source Files" FILES ${SOURCES})
    source_group("Header Files" FILES ${HEADERS})
    
    # 设置启动项目
    set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT ${PROJECT_NAME})
    
    # 设置工作目录
    set_property(TARGET ${PROJECT_NAME} PROPERTY VS_DEBUGGER_WORKING_DIRECTORY "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
endif()

# 打印配置信息
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Output Directory: ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")

# 编译说明
message(STATUS "")
message(STATUS "=== 编译说明 ===")
message(STATUS "1. 确保已安装Visual Studio 2019或更高版本")
message(STATUS "2. 确保已安装Windows SDK")
message(STATUS "3. 使用以下命令编译：")
message(STATUS "   mkdir build && cd build")
message(STATUS "   cmake .. -G \"Visual Studio 16 2019\" -A Win32")
message(STATUS "   cmake --build . --config Release")
message(STATUS "4. 或者直接在Visual Studio中打开生成的.sln文件")
message(STATUS "================")

# 添加自定义目标用于清理
add_custom_target(clean-all
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/bin
    COMMAND ${CMAKE_COMMAND} -E remove_directory ${CMAKE_BINARY_DIR}/lib
    COMMENT "Cleaning all output directories"
)

# 添加文档生成目标（如果安装了Doxygen）
find_package(Doxygen QUIET)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
    
    if(EXISTS ${DOXYGEN_IN})
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(docs
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# 添加测试（如果需要的话）
# enable_testing()
# add_subdirectory(tests)
