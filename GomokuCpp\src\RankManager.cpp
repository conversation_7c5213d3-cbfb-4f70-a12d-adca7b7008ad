/**
 * @file RankManager.cpp
 * @brief RankManager类的实现文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件实现了RankManager类的所有方法。
 * RankManager负责管理五子棋游戏的排行榜功能。
 */

#include "RankManager.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <stdexcept>

// ==================== RankRecord 结构体实现 ====================

/**
 * @brief RankRecord默认构造函数
 * 
 * 创建一个空的排行榜记录。
 */
RankRecord::RankRecord() : playerName(""), steps(0), date("") {
    // 使用初始化列表初始化成员变量
}

/**
 * @brief RankRecord带参数构造函数
 * 
 * @param name 玩家姓名
 * @param stepCount 获胜步数
 * @param gameDate 游戏日期
 */
RankRecord::RankRecord(const std::string& name, int stepCount, const std::string& gameDate)
    : playerName(name), steps(stepCount), date(gameDate) {
    // 使用初始化列表，这是C++推荐的方式
}

/**
 * @brief 比较运算符重载
 * 
 * @param other 要比较的记录
 * @return 如果当前记录更好（步数更少）返回true
 * 
 * 排序规则：
 * 1. 首先按步数升序排列（步数越少越好）
 * 2. 步数相同时按姓名字典序排列
 */
bool RankRecord::operator<(const RankRecord& other) const {
    if (steps != other.steps) {
        return steps < other.steps;  // 步数少的排在前面
    }
    return playerName < other.playerName;  // 步数相同时按姓名排序
}

// ==================== RankManager 类实现 ====================

/**
 * @brief 默认构造函数
 * 
 * 创建一个使用默认文件名的排行榜管理器。
 */
RankManager::RankManager() : filename_("ranklist.dat") {
    // 预留空间以提高性能
    records_.reserve(MAX_RECORDS);
}

/**
 * @brief 带文件名的构造函数
 * 
 * @param filename 排行榜数据文件名
 */
RankManager::RankManager(const std::string& filename) : filename_(filename) {
    records_.reserve(MAX_RECORDS);
}

/**
 * @brief 析构函数
 * 
 * 确保程序结束时数据被保存到文件。
 */
RankManager::~RankManager() {
    // 自动保存数据，确保不丢失
    saveToFile();
}

/**
 * @brief 添加新的排行榜记录
 * 
 * @param playerName 玩家姓名
 * @param steps 获胜步数
 * @return 如果成功添加返回true
 * 
 * 这是排行榜管理的核心方法。
 * 它会自动维护排行榜的有序性和大小限制。
 */
bool RankManager::addRecord(const std::string& playerName, int steps) {
    // 输入验证
    if (playerName.empty() || steps <= 0) {
        return false;  // 无效输入
    }
    
    // 创建新记录
    RankRecord newRecord(playerName, steps, getCurrentDate());
    
    // 添加到记录列表
    records_.push_back(newRecord);
    
    // 重新排序
    sortRecords();
    
    // 如果超过最大记录数，删除最后的记录
    if (records_.size() > MAX_RECORDS) {
        records_.pop_back();  // 删除最后一个元素
    }
    
    return true;
}

/**
 * @brief 判断指定步数是否能进入排行榜
 * 
 * @param steps 要检查的步数
 * @return 如果能进入排行榜返回true
 * 
 * 这个方法用于在游戏结束时快速判断是否需要提示玩家输入姓名。
 */
bool RankManager::canEnterRank(int steps) const {
    // 如果排行榜未满，任何成绩都能进入
    if (records_.size() < MAX_RECORDS) {
        return true;
    }
    
    // 如果排行榜已满，检查是否比最后一名更好
    return steps < records_.back().steps;
}

/**
 * @brief 获取指定步数在排行榜中的排名
 * 
 * @param steps 要查询的步数
 * @return 返回排名（1-based），如果不能进入返回-1
 * 
 * 这个方法计算新成绩在排行榜中的位置。
 */
int RankManager::getRankPosition(int steps) const {
    if (!canEnterRank(steps)) {
        return -1;  // 不能进入排行榜
    }
    
    // 计算排名位置
    int position = 1;
    for (const auto& record : records_) {
        if (steps >= record.steps) {
            position++;
        } else {
            break;  // 找到插入位置
        }
    }
    
    return position;
}

/**
 * @brief 从文件加载排行榜数据
 * 
 * @param filename 数据文件名
 * @return 如果加载成功返回true
 * 
 * 文件格式：每行一个记录，格式为 "姓名 步数 日期"
 * 例如：张三 25 2024-01-01
 */
bool RankManager::loadFromFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return false;  // 文件打开失败
    }
    
    records_.clear();  // 清空现有记录
    
    std::string line;
    while (std::getline(file, line) && records_.size() < MAX_RECORDS) {
        std::istringstream iss(line);
        std::string name, date;
        int steps;
        
        // 解析每行数据
        if (iss >> name >> steps >> date) {
            records_.emplace_back(name, steps, date);
        }
    }
    
    file.close();
    
    // 确保数据有序
    sortRecords();
    
    return true;
}

/**
 * @brief 保存排行榜数据到文件
 * 
 * @param filename 数据文件名
 * @return 如果保存成功返回true
 */
bool RankManager::saveToFile(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        return false;  // 文件创建失败
    }
    
    // 写入每条记录
    for (const auto& record : records_) {
        file << record.playerName << " " 
             << record.steps << " " 
             << record.date << "\n";
    }
    
    file.close();
    return true;
}

/**
 * @brief 使用默认文件名加载数据
 * 
 * @return 如果加载成功返回true
 */
bool RankManager::loadFromFile() {
    return loadFromFile(filename_);
}

/**
 * @brief 使用默认文件名保存数据
 * 
 * @return 如果保存成功返回true
 */
bool RankManager::saveToFile() {
    return saveToFile(filename_);
}

/**
 * @brief 获取排行榜记录数量
 *
 * @return 返回当前记录数量
 */
int RankManager::getRecordCount() const {
    return static_cast<int>(records_.size());
}

/**
 * @brief 获取指定位置的记录
 *
 * @param index 记录索引 (0-based)
 * @return 返回记录的引用
 * @throws std::out_of_range 如果索引无效
 */
const RankRecord& RankManager::getRecord(int index) const {
    if (index < 0 || index >= static_cast<int>(records_.size())) {
        throw std::out_of_range("记录索引超出范围");
    }
    return records_[index];
}

/**
 * @brief 获取所有记录
 *
 * @return 返回记录列表的常量引用
 *
 * 返回const引用避免不必要的拷贝，同时防止外部修改。
 */
const std::vector<RankRecord>& RankManager::getAllRecords() const {
    return records_;
}

/**
 * @brief 清空所有记录
 *
 * 这个方法用于重置排行榜。
 */
void RankManager::clearRecords() {
    records_.clear();
}

/**
 * @brief 获取排行榜的字符串表示
 *
 * @return 返回格式化的排行榜字符串
 *
 * 生成一个美观的排行榜显示格式，
 * 可以直接用于控制台输出或GUI显示。
 */
std::string RankManager::toString() const {
    std::ostringstream oss;

    // 表头
    oss << "==================== 排行榜 ====================\n";
    oss << "排名  姓名          步数    日期\n";
    oss << "----------------------------------------------\n";

    // 如果没有记录
    if (records_.empty()) {
        oss << "          暂无记录\n";
    } else {
        // 输出每条记录
        for (size_t i = 0; i < records_.size(); ++i) {
            const auto& record = records_[i];
            oss << std::setw(2) << (i + 1) << "    "           // 排名
                << std::setw(12) << std::left << record.playerName  // 姓名
                << std::setw(6) << std::right << record.steps       // 步数
                << "    " << record.date << "\n";                   // 日期
        }
    }

    oss << "==============================================\n";
    return oss.str();
}

/**
 * @brief 设置数据文件名
 *
 * @param filename 新的文件名
 */
void RankManager::setFilename(const std::string& filename) {
    filename_ = filename;
}

/**
 * @brief 获取当前数据文件名
 *
 * @return 返回当前文件名
 */
std::string RankManager::getFilename() const {
    return filename_;
}

/**
 * @brief 对排行榜记录进行排序
 *
 * 这是一个私有方法，在添加新记录后自动调用。
 * 使用STL的sort算法，配合RankRecord的operator<进行排序。
 */
void RankManager::sortRecords() {
    std::sort(records_.begin(), records_.end());
}

/**
 * @brief 获取当前日期字符串
 *
 * @return 返回格式化的日期字符串 (YYYY-MM-DD)
 *
 * 这个方法使用C++标准库获取当前系统时间，
 * 并格式化为可读的日期字符串。
 */
std::string RankManager::getCurrentDate() const {
    // 获取当前时间
    std::time_t now = std::time(nullptr);
    std::tm* timeinfo = std::localtime(&now);

    // 格式化为字符串
    std::ostringstream oss;
    oss << std::setfill('0')
        << std::setw(4) << (timeinfo->tm_year + 1900) << "-"  // 年
        << std::setw(2) << (timeinfo->tm_mon + 1) << "-"      // 月
        << std::setw(2) << timeinfo->tm_mday;                 // 日

    return oss.str();
}
