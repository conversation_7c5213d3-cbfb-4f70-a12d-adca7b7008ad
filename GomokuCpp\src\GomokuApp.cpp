/**
 * @file GomokuApp.cpp
 * @brief GomokuApp类的实现文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件实现了GomokuApp类的所有方法。
 * GomokuApp是五子棋GUI应用程序的主类，负责Windows界面的创建和管理。
 */

#include "GomokuApp.h"
#include <windowsx.h>
#include <commctrl.h>
#include <sstream>
#include <iomanip>

// 链接必要的库
#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")

// 窗口类名常量
const wchar_t* WINDOW_CLASS_NAME = L"GomokuGameWindow";

/**
 * @brief 构造函数实现
 * 
 * 初始化所有成员变量为默认值。
 * 使用初始化列表是C++的最佳实践。
 */
GomokuApp::GomokuApp() 
    : hInstance_(nullptr), 
      hMainWnd_(nullptr), 
      hStatusBar_(nullptr),
      gameController_(nullptr) {
    // 构造函数体为空，所有初始化都在初始化列表中完成
}

/**
 * @brief 析构函数实现
 * 
 * 清理资源，确保程序正常退出。
 * 智能指针会自动清理gameController_，但我们仍然显式重置以确保顺序。
 */
GomokuApp::~GomokuApp() {
    // 重置智能指针，确保GameController在窗口销毁前被清理
    gameController_.reset();
}

/**
 * @brief 初始化应用程序
 * 
 * @param hInstance 应用程序实例句柄
 * @return 如果初始化成功返回true
 * 
 * 这是应用程序的初始化入口点，按顺序执行以下操作：
 * 1. 保存实例句柄
 * 2. 初始化公共控件
 * 3. 创建游戏控制器
 * 4. 注册窗口类
 * 5. 创建主窗口和子控件
 */
bool GomokuApp::Initialize(HINSTANCE hInstance) {
    hInstance_ = hInstance;
    
    // 初始化公共控件库（用于状态栏等控件）
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_BAR_CLASSES;  // 状态栏控件
    InitCommonControlsEx(&icex);
    
    // 创建游戏控制器
    gameController_ = std::make_unique<GameController>();
    if (!gameController_->initialize()) {
        MessageBox(nullptr, L"游戏控制器初始化失败！", L"错误", MB_OK | MB_ICONERROR);
        return false;
    }
    
    // 注册窗口类
    if (!RegisterWindowClass()) {
        MessageBox(nullptr, L"窗口类注册失败！", L"错误", MB_OK | MB_ICONERROR);
        return false;
    }
    
    // 创建主窗口
    if (!CreateMainWindow()) {
        MessageBox(nullptr, L"主窗口创建失败！", L"错误", MB_OK | MB_ICONERROR);
        return false;
    }
    
    // 创建菜单栏
    CreateMenuBar();
    
    // 创建状态栏
    CreateStatusBar();
    
    // 更新状态栏显示
    UpdateStatusBar();
    
    return true;
}

/**
 * @brief 运行应用程序
 * 
 * @return 返回程序退出代码
 * 
 * 启动Windows消息循环，这是GUI应用程序的核心。
 * 消息循环会一直运行，直到收到WM_QUIT消息。
 */
int GomokuApp::Run() {
    // 显示主窗口
    ShowWindow(hMainWnd_, SW_SHOW);
    UpdateWindow(hMainWnd_);
    
    // 消息循环
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);  // 转换键盘消息
        DispatchMessage(&msg);   // 分发消息到窗口过程
    }
    
    return static_cast<int>(msg.wParam);
}

/**
 * @brief 注册窗口类
 * 
 * @return 如果注册成功返回true
 * 
 * 向Windows系统注册我们的窗口类，定义窗口的基本属性。
 * 这是创建窗口之前必须执行的步骤。
 */
bool GomokuApp::RegisterWindowClass() {
    WNDCLASSEX wc = {};
    
    // 设置窗口类的各种属性
    wc.cbSize = sizeof(WNDCLASSEX);                    // 结构体大小
    wc.style = CS_HREDRAW | CS_VREDRAW;                // 窗口样式
    wc.lpfnWndProc = WindowProc;                       // 窗口过程函数
    wc.cbClsExtra = 0;                                 // 类额外内存
    wc.cbWndExtra = sizeof(GomokuApp*);                // 窗口额外内存（存储this指针）
    wc.hInstance = hInstance_;                         // 实例句柄
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);     // 大图标
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);       // 鼠标光标
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);     // 背景画刷
    wc.lpszMenuName = nullptr;                         // 菜单名称（我们手动创建菜单）
    wc.lpszClassName = WINDOW_CLASS_NAME;              // 窗口类名
    wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);   // 小图标
    
    // 注册窗口类
    return RegisterClassEx(&wc) != 0;
}

/**
 * @brief 创建主窗口
 * 
 * @return 如果创建成功返回true
 * 
 * 创建应用程序的主窗口，设置窗口大小、位置等属性。
 * 窗口大小根据棋盘大小计算得出。
 */
bool GomokuApp::CreateMainWindow() {
    // 创建窗口
    hMainWnd_ = CreateWindowEx(
        0,                              // 扩展样式
        WINDOW_CLASS_NAME,              // 窗口类名
        L"五子棋游戏 - C++版",           // 窗口标题
        WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,  // 窗口样式（不可最大化和调整大小）
        CW_USEDEFAULT,                  // X坐标（系统决定）
        CW_USEDEFAULT,                  // Y坐标（系统决定）
        WINDOW_WIDTH,                   // 窗口宽度
        WINDOW_HEIGHT,                  // 窗口高度
        nullptr,                        // 父窗口
        nullptr,                        // 菜单句柄
        hInstance_,                     // 实例句柄
        this                            // 创建参数（传递this指针）
    );
    
    return hMainWnd_ != nullptr;
}

/**
 * @brief 创建菜单栏
 * 
 * 创建应用程序的菜单栏，包括游戏菜单和帮助菜单。
 * 菜单提供了游戏的主要功能入口。
 */
void GomokuApp::CreateMenuBar() {
    // 创建主菜单栏
    HMENU hMenuBar = CreateMenu();
    
    // 创建游戏菜单
    HMENU hGameMenu = CreatePopupMenu();
    AppendMenu(hGameMenu, MF_STRING, ID_NEW_GAME, L"新游戏(&N)\tF2");
    AppendMenu(hGameMenu, MF_STRING, ID_RESTART, L"重新开始(&R)\tF5");
    AppendMenu(hGameMenu, MF_SEPARATOR, 0, nullptr);
    AppendMenu(hGameMenu, MF_STRING, ID_RANK_LIST, L"排行榜(&L)");
    AppendMenu(hGameMenu, MF_SEPARATOR, 0, nullptr);
    AppendMenu(hGameMenu, MF_STRING, ID_EXIT, L"退出(&X)\tAlt+F4");
    
    // 创建帮助菜单
    HMENU hHelpMenu = CreatePopupMenu();
    AppendMenu(hHelpMenu, MF_STRING, ID_GAME_RULES, L"游戏规则(&R)\tF1");
    AppendMenu(hHelpMenu, MF_SEPARATOR, 0, nullptr);
    AppendMenu(hHelpMenu, MF_STRING, ID_ABOUT, L"关于(&A)");
    
    // 将子菜单添加到主菜单栏
    AppendMenu(hMenuBar, MF_POPUP, (UINT_PTR)hGameMenu, L"游戏(&G)");
    AppendMenu(hMenuBar, MF_POPUP, (UINT_PTR)hHelpMenu, L"帮助(&H)");
    
    // 设置窗口菜单
    SetMenu(hMainWnd_, hMenuBar);
}

/**
 * @brief 创建状态栏
 * 
 * 创建窗口底部的状态栏，用于显示游戏状态信息。
 * 状态栏会显示当前玩家、游戏消息等信息。
 */
void GomokuApp::CreateStatusBar() {
    hStatusBar_ = CreateWindowEx(
        0,                              // 扩展样式
        STATUSCLASSNAME,                // 状态栏类名
        nullptr,                        // 窗口文本
        WS_CHILD | WS_VISIBLE | SBARS_SIZEGRIP,  // 窗口样式
        0, 0, 0, 0,                     // 位置和大小（自动计算）
        hMainWnd_,                      // 父窗口
        nullptr,                        // 菜单句柄
        hInstance_,                     // 实例句柄
        nullptr                         // 创建参数
    );
}

/**
 * @brief 绘制棋盘
 *
 * @param hdc 设备上下文句柄
 *
 * 在指定的设备上下文上绘制15x15的棋盘网格。
 * 包括网格线、天元点、星位等标准五子棋棋盘元素。
 */
void GomokuApp::DrawBoard(HDC hdc) {
    // 创建画笔用于绘制网格线
    HPEN hPen = CreatePen(PS_SOLID, 1, RGB(0, 0, 0));
    HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);

    // 绘制垂直网格线
    for (int i = 0; i < BOARD_SIZE; ++i) {
        int x = BOARD_MARGIN + i * CELL_SIZE;
        MoveToEx(hdc, x, BOARD_MARGIN, nullptr);
        LineTo(hdc, x, BOARD_MARGIN + (BOARD_SIZE - 1) * CELL_SIZE);
    }

    // 绘制水平网格线
    for (int i = 0; i < BOARD_SIZE; ++i) {
        int y = BOARD_MARGIN + i * CELL_SIZE;
        MoveToEx(hdc, BOARD_MARGIN, y, nullptr);
        LineTo(hdc, BOARD_MARGIN + (BOARD_SIZE - 1) * CELL_SIZE, y);
    }

    // 恢复原来的画笔
    SelectObject(hdc, hOldPen);
    DeleteObject(hPen);

    // 绘制天元点和星位
    HBRUSH hBrush = CreateSolidBrush(RGB(0, 0, 0));
    HBRUSH hOldBrush = (HBRUSH)SelectObject(hdc, hBrush);

    // 天元点（棋盘中心）
    int centerX = BOARD_MARGIN + 7 * CELL_SIZE;
    int centerY = BOARD_MARGIN + 7 * CELL_SIZE;
    Ellipse(hdc, centerX - 3, centerY - 3, centerX + 3, centerY + 3);

    // 四个星位（棋盘的四个角落附近）
    int starPositions[][2] = {{3, 3}, {3, 11}, {11, 3}, {11, 11}};
    for (int i = 0; i < 4; ++i) {
        int x = BOARD_MARGIN + starPositions[i][0] * CELL_SIZE;
        int y = BOARD_MARGIN + starPositions[i][1] * CELL_SIZE;
        Ellipse(hdc, x - 2, y - 2, x + 2, y + 2);
    }

    // 恢复原来的画刷
    SelectObject(hdc, hOldBrush);
    DeleteObject(hBrush);
}

/**
 * @brief 绘制棋子
 *
 * @param hdc 设备上下文句柄
 * @param row 行坐标
 * @param col 列坐标
 * @param pieceType 棋子类型
 *
 * 在指定位置绘制黑子或白子。
 * 黑子用黑色填充，白子用白色填充但有黑色边框。
 */
void GomokuApp::DrawPiece(HDC hdc, int row, int col, PieceType pieceType) {
    if (pieceType == PieceType::EMPTY) {
        return;  // 空位置不绘制
    }

    // 计算棋子的屏幕坐标
    int x, y;
    BoardToScreenCoords(row, col, x, y);

    // 创建画刷和画笔
    HBRUSH hBrush;
    if (pieceType == PieceType::BLACK) {
        hBrush = CreateSolidBrush(RGB(0, 0, 0));      // 黑色画刷
    } else {
        hBrush = CreateSolidBrush(RGB(255, 255, 255)); // 白色画刷
    }

    HPEN hPen = CreatePen(PS_SOLID, 1, RGB(0, 0, 0));  // 黑色边框

    // 选择画刷和画笔
    HBRUSH hOldBrush = (HBRUSH)SelectObject(hdc, hBrush);
    HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);

    // 绘制圆形棋子
    Ellipse(hdc, x - STONE_RADIUS, y - STONE_RADIUS,
            x + STONE_RADIUS, y + STONE_RADIUS);

    // 恢复原来的画刷和画笔
    SelectObject(hdc, hOldBrush);
    SelectObject(hdc, hOldPen);

    // 清理资源
    DeleteObject(hBrush);
    DeleteObject(hPen);
}

/**
 * @brief 绘制所有棋子
 *
 * @param hdc 设备上下文句柄
 *
 * 遍历整个棋盘，绘制所有已下的棋子。
 * 这个方法在重绘窗口时调用。
 */
void GomokuApp::DrawAllPieces(HDC hdc) {
    for (int row = 0; row < BOARD_SIZE; ++row) {
        for (int col = 0; col < BOARD_SIZE; ++col) {
            PieceType piece = gameController_->getPiece(row, col);
            if (piece != PieceType::EMPTY) {
                DrawPiece(hdc, row, col, piece);
            }
        }
    }
}

/**
 * @brief 高亮显示最后一步棋
 *
 * @param hdc 设备上下文句柄
 *
 * 在最后下的棋子周围绘制红色方框，帮助玩家识别最新的棋步。
 */
void GomokuApp::HighlightLastMove(HDC hdc) {
    if (gameController_->getTotalMoves() == 0) {
        return;  // 没有棋子时不绘制
    }

    Point lastMove = gameController_->getLastMove();
    int x, y;
    BoardToScreenCoords(lastMove.getRow(), lastMove.getCol(), x, y);

    // 创建红色画笔
    HPEN hPen = CreatePen(PS_SOLID, 2, RGB(255, 0, 0));
    HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);

    // 设置透明背景
    int oldBkMode = SetBkMode(hdc, TRANSPARENT);

    // 绘制高亮方框
    Rectangle(hdc, x - STONE_RADIUS - 2, y - STONE_RADIUS - 2,
              x + STONE_RADIUS + 2, y + STONE_RADIUS + 2);

    // 恢复设置
    SetBkMode(hdc, oldBkMode);
    SelectObject(hdc, hOldPen);
    DeleteObject(hPen);
}

/**
 * @brief 将屏幕坐标转换为棋盘坐标
 *
 * @param x 屏幕X坐标
 * @param y 屏幕Y坐标
 * @param row 输出参数：棋盘行坐标
 * @param col 输出参数：棋盘列坐标
 * @return 如果转换成功返回true
 *
 * 将鼠标点击的屏幕坐标转换为棋盘上的行列坐标。
 * 使用就近原则，点击格子附近会自动对齐到最近的交叉点。
 */
bool GomokuApp::ScreenToBoardCoords(int x, int y, int& row, int& col) {
    // 计算相对于棋盘左上角的坐标
    int relativeX = x - BOARD_MARGIN;
    int relativeY = y - BOARD_MARGIN;

    // 转换为棋盘坐标（四舍五入到最近的交叉点）
    col = (relativeX + CELL_SIZE / 2) / CELL_SIZE;
    row = (relativeY + CELL_SIZE / 2) / CELL_SIZE;

    // 检查坐标是否在有效范围内
    return (row >= 0 && row < BOARD_SIZE && col >= 0 && col < BOARD_SIZE);
}

/**
 * @brief 将棋盘坐标转换为屏幕坐标
 *
 * @param row 棋盘行坐标
 * @param col 棋盘列坐标
 * @param x 输出参数：屏幕X坐标
 * @param y 输出参数：屏幕Y坐标
 *
 * 将棋盘坐标转换为屏幕绘图坐标。
 * 返回的坐标是交叉点的中心位置。
 */
void GomokuApp::BoardToScreenCoords(int row, int col, int& x, int& y) {
    x = BOARD_MARGIN + col * CELL_SIZE;
    y = BOARD_MARGIN + row * CELL_SIZE;
}

/**
 * @brief 处理鼠标左键点击事件
 *
 * @param x 鼠标X坐标
 * @param y 鼠标Y坐标
 *
 * 处理用户的下棋操作：
 * 1. 将屏幕坐标转换为棋盘坐标
 * 2. 检查是否可以在该位置下棋
 * 3. 调用游戏控制器执行下棋操作
 * 4. 更新界面显示
 */
void GomokuApp::HandleMouseClick(int x, int y) {
    // 只有在游戏进行中才能下棋
    if (gameController_->getCurrentState() != GameState::PLAYING) {
        return;
    }

    // 转换坐标
    int row, col;
    if (!ScreenToBoardCoords(x, y, row, col)) {
        return;  // 点击位置不在棋盘范围内
    }

    // 尝试下棋
    if (gameController_->makeMove(row, col)) {
        // 下棋成功，重绘窗口
        InvalidateRect(hMainWnd_, nullptr, TRUE);

        // 更新状态栏
        UpdateStatusBar();

        // 检查游戏是否结束
        GameResult result = gameController_->getGameResult();
        if (result != GameResult::CONTINUE) {
            ShowGameEndDialog(result);
        }
    } else {
        // 下棋失败，显示错误消息
        MessageBox(hMainWnd_, L"无法在此位置下棋！", L"提示", MB_OK | MB_ICONINFORMATION);
    }
}

/**
 * @brief 处理菜单命令
 *
 * @param menuId 菜单项ID
 *
 * 根据用户选择的菜单项执行相应的操作。
 */
void GomokuApp::HandleMenuCommand(int menuId) {
    switch (menuId) {
        case ID_NEW_GAME:
            // 开始新游戏
            gameController_->startNewGame();
            InvalidateRect(hMainWnd_, nullptr, TRUE);
            UpdateStatusBar();
            break;

        case ID_RESTART:
            // 重新开始当前游戏
            gameController_->resetGame();
            gameController_->startNewGame();
            InvalidateRect(hMainWnd_, nullptr, TRUE);
            UpdateStatusBar();
            break;

        case ID_RANK_LIST:
            // 显示排行榜
            ShowRankDialog();
            break;

        case ID_GAME_RULES:
            // 显示游戏规则
            ShowRulesDialog();
            break;

        case ID_ABOUT:
            // 显示关于对话框
            ShowAboutDialog();
            break;

        case ID_EXIT:
            // 退出程序
            PostMessage(hMainWnd_, WM_CLOSE, 0, 0);
            break;

        default:
            break;
    }
}

/**
 * @brief 更新状态栏显示
 *
 * 根据当前游戏状态更新状态栏的文本显示。
 * 显示当前玩家、游戏消息等信息。
 */
void GomokuApp::UpdateStatusBar() {
    if (!hStatusBar_) {
        return;
    }

    std::wostringstream woss;

    // 根据游戏状态显示不同信息
    switch (gameController_->getCurrentState()) {
        case GameState::PLAYING: {
            // 显示当前玩家
            if (gameController_->getCurrentPlayer() == PieceType::BLACK) {
                woss << L"当前玩家: 黑子 (●)";
            } else {
                woss << L"当前玩家: 白子 (○)";
            }
            woss << L"  |  步数: " << gameController_->getTotalMoves();
            break;
        }
        case GameState::GAME_OVER: {
            // 显示游戏结果
            GameResult result = gameController_->getGameResult();
            if (result == GameResult::BLACK_WIN) {
                woss << L"游戏结束 - 黑子获胜！";
            } else if (result == GameResult::WHITE_WIN) {
                woss << L"游戏结束 - 白子获胜！";
            } else if (result == GameResult::DRAW) {
                woss << L"游戏结束 - 平局！";
            }
            woss << L"  |  总步数: " << gameController_->getTotalMoves();
            break;
        }
        default:
            woss << L"欢迎来到五子棋游戏！点击"游戏"菜单开始新游戏。";
            break;
    }

    // 设置状态栏文本
    SendMessage(hStatusBar_, SB_SETTEXT, 0, (LPARAM)woss.str().c_str());
}

/**
 * @brief 显示游戏结束对话框
 *
 * @param result 游戏结果
 *
 * 当游戏结束时显示结果对话框，并处理排行榜相关操作。
 * 如果玩家获胜且成绩能进入排行榜，会提示输入姓名。
 */
void GomokuApp::ShowGameEndDialog(GameResult result) {
    std::wstring message;
    std::wstring title = L"游戏结束";

    // 根据游戏结果生成消息
    switch (result) {
        case GameResult::BLACK_WIN:
            message = L"恭喜！黑子获胜！\n总步数: " + std::to_wstring(gameController_->getTotalMoves());
            break;
        case GameResult::WHITE_WIN:
            message = L"恭喜！白子获胜！\n总步数: " + std::to_wstring(gameController_->getTotalMoves());
            break;
        case GameResult::DRAW:
            message = L"游戏平局！\n总步数: " + std::to_wstring(gameController_->getTotalMoves());
            break;
        default:
            message = L"游戏结束！";
            break;
    }

    // 显示结果对话框
    MessageBox(hMainWnd_, message.c_str(), title.c_str(), MB_OK | MB_ICONINFORMATION);

    // 如果有获胜者且能进入排行榜，询问玩家姓名
    if ((result == GameResult::BLACK_WIN || result == GameResult::WHITE_WIN) &&
        gameController_->canEnterRank(gameController_->getTotalMoves())) {

        int position = gameController_->getRankPosition(gameController_->getTotalMoves());
        std::wstring rankMessage = L"您的成绩可以进入排行榜第 " + std::to_wstring(position) + L" 位！\n是否要记录您的姓名？";

        if (MessageBox(hMainWnd_, rankMessage.c_str(), L"进入排行榜", MB_YESNO | MB_ICONQUESTION) == IDYES) {
            std::string playerName;
            if (GetPlayerName(playerName)) {
                gameController_->addRankRecord(playerName, gameController_->getTotalMoves());
                MessageBox(hMainWnd_, L"恭喜！您的成绩已记录到排行榜！", L"成功", MB_OK | MB_ICONINFORMATION);
            }
        }
    }

    // 设置游戏状态为结束
    gameController_->setState(GameState::GAME_OVER);
    UpdateStatusBar();
}

/**
 * @brief 显示排行榜对话框
 *
 * 显示当前的排行榜信息。
 * 将排行榜数据格式化为易读的文本显示。
 */
void GomokuApp::ShowRankDialog() {
    std::string rankStr = gameController_->getRankString();

    // 将UTF-8字符串转换为宽字符串
    int wideSize = MultiByteToWideChar(CP_UTF8, 0, rankStr.c_str(), -1, nullptr, 0);
    std::wstring wideRankStr(wideSize, 0);
    MultiByteToWideChar(CP_UTF8, 0, rankStr.c_str(), -1, &wideRankStr[0], wideSize);

    MessageBox(hMainWnd_, wideRankStr.c_str(), L"排行榜", MB_OK | MB_ICONINFORMATION);
}

/**
 * @brief 显示游戏规则对话框
 *
 * 显示五子棋游戏的规则说明。
 */
void GomokuApp::ShowRulesDialog() {
    std::string rulesStr = gameController_->getGameRules();

    // 将UTF-8字符串转换为宽字符串
    int wideSize = MultiByteToWideChar(CP_UTF8, 0, rulesStr.c_str(), -1, nullptr, 0);
    std::wstring wideRulesStr(wideSize, 0);
    MultiByteToWideChar(CP_UTF8, 0, rulesStr.c_str(), -1, &wideRulesStr[0], wideSize);

    MessageBox(hMainWnd_, wideRulesStr.c_str(), L"游戏规则", MB_OK | MB_ICONINFORMATION);
}

/**
 * @brief 显示关于对话框
 *
 * 显示程序的版本信息和作者信息。
 */
void GomokuApp::ShowAboutDialog() {
    std::wstring aboutText =
        L"五子棋游戏 - C++版\n"
        L"版本: 1.0\n"
        L"开发语言: C++\n"
        L"界面框架: Win32 API\n\n"
        L"这是一个使用C++和Win32 API开发的五子棋游戏。\n"
        L"采用面向对象设计，代码结构清晰，\n"
        L"适合作为C++编程学习的参考项目。\n\n"
        L"功能特点：\n"
        L"• 标准15×15五子棋规则\n"
        L"• 图形化用户界面\n"
        L"• 排行榜系统\n"
        L"• 完整的游戏逻辑\n\n"
        L"作者: 学生姓名\n"
        L"日期: 2024年";

    MessageBox(hMainWnd_, aboutText.c_str(), L"关于", MB_OK | MB_ICONINFORMATION);
}

/**
 * @brief 获取用户输入的姓名
 *
 * @param playerName 输出参数：用户输入的姓名
 * @return 如果用户确认输入返回true
 *
 * 弹出输入对话框，让用户输入姓名用于排行榜记录。
 * 这里使用简单的InputBox实现，实际项目中可以创建自定义对话框。
 */
bool GomokuApp::GetPlayerName(std::string& playerName) {
    // 简化实现：使用默认姓名
    // 在实际项目中，这里应该弹出一个输入对话框
    playerName = "Player";

    // 可以在这里添加更复杂的输入对话框实现
    // 例如使用DialogBox创建自定义对话框

    return true;
}

/**
 * @brief 静态窗口过程函数
 *
 * @param hwnd 窗口句柄
 * @param uMsg 消息类型
 * @param wParam 消息参数1
 * @param lParam 消息参数2
 * @return 消息处理结果
 *
 * 这是Windows消息处理的入口点。
 * 由于Windows API是C风格的，需要一个静态函数作为回调。
 * 这个函数负责将消息转发给相应的GomokuApp实例。
 */
LRESULT CALLBACK GomokuApp::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    GomokuApp* pApp = nullptr;

    if (uMsg == WM_NCCREATE) {
        // 窗口创建时，从lParam中获取this指针并存储到窗口额外内存中
        CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
        pApp = reinterpret_cast<GomokuApp*>(pCreate->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(pApp));
    } else {
        // 从窗口额外内存中获取this指针
        pApp = reinterpret_cast<GomokuApp*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }

    if (pApp) {
        // 调用实例方法处理消息
        return pApp->HandleMessage(hwnd, uMsg, wParam, lParam);
    } else {
        // 如果没有实例指针，使用默认处理
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
}

/**
 * @brief 实例窗口过程函数
 *
 * @param hwnd 窗口句柄
 * @param uMsg 消息类型
 * @param wParam 消息参数1
 * @param lParam 消息参数2
 * @return 消息处理结果
 *
 * 实际的消息处理函数，处理各种Windows消息。
 * 这里处理绘图、鼠标点击、菜单选择、窗口大小变化等消息。
 */
LRESULT GomokuApp::HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_CREATE:
            // 窗口创建完成
            return 0;

        case WM_PAINT: {
            // 窗口重绘
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);

            // 绘制棋盘
            DrawBoard(hdc);

            // 绘制所有棋子
            DrawAllPieces(hdc);

            // 高亮最后一步棋
            HighlightLastMove(hdc);

            EndPaint(hwnd, &ps);
            return 0;
        }

        case WM_LBUTTONDOWN: {
            // 鼠标左键点击
            int x = GET_X_LPARAM(lParam);
            int y = GET_Y_LPARAM(lParam);
            HandleMouseClick(x, y);
            return 0;
        }

        case WM_COMMAND: {
            // 菜单命令或控件消息
            int menuId = LOWORD(wParam);
            HandleMenuCommand(menuId);
            return 0;
        }

        case WM_SIZE: {
            // 窗口大小改变
            if (hStatusBar_) {
                // 调整状态栏大小
                SendMessage(hStatusBar_, WM_SIZE, 0, 0);
            }
            return 0;
        }

        case WM_KEYDOWN: {
            // 键盘按键
            switch (wParam) {
                case VK_F1:
                    // F1 - 显示游戏规则
                    HandleMenuCommand(ID_GAME_RULES);
                    break;
                case VK_F2:
                    // F2 - 新游戏
                    HandleMenuCommand(ID_NEW_GAME);
                    break;
                case VK_F5:
                    // F5 - 重新开始
                    HandleMenuCommand(ID_RESTART);
                    break;
                default:
                    break;
            }
            return 0;
        }

        case WM_CLOSE:
            // 窗口关闭
            if (gameController_) {
                gameController_->exitGame();  // 保存数据
            }
            DestroyWindow(hwnd);
            return 0;

        case WM_DESTROY:
            // 窗口销毁
            PostQuitMessage(0);
            return 0;

        default:
            // 其他消息使用默认处理
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
}
