/**
 * @file GomokuApp.h
 * @brief 五子棋游戏Windows GUI应用程序主类
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件定义了GomokuApp类，它是整个五子棋GUI应用程序的主类。
 * 该类负责创建和管理Windows窗口，处理用户界面事件，
 * 并与游戏逻辑控制器进行交互。
 * 
 * 设计特点：
 * - 使用Win32 API创建原生Windows应用程序
 * - 采用面向对象的设计，封装窗口操作
 * - 实现MVC模式，分离界面和逻辑
 * - 提供完整的游戏功能和用户交互
 */

#ifndef GOMOKUAPP_H
#define GOMOKUAPP_H

#include <windows.h>
#include <string>
#include <memory>
#include "GameController.h"

/**
 * @brief 五子棋GUI应用程序类
 * 
 * 这个类封装了整个Windows GUI应用程序的功能。
 * 它管理主窗口的创建、消息处理、绘图操作等。
 * 
 * 主要职责：
 * 1. 窗口管理：创建、显示、销毁主窗口
 * 2. 事件处理：处理鼠标点击、菜单选择等用户操作
 * 3. 绘图功能：绘制棋盘、棋子、界面元素
 * 4. 游戏交互：与GameController交互，更新游戏状态
 * 5. 对话框管理：显示消息框、输入对话框等
 * 
 * 使用示例：
 * GomokuApp app;
 * if (app.Initialize(hInstance)) {
 *     app.Run();
 * }
 */
class GomokuApp {
private:
    // 窗口相关成员变量
    HINSTANCE hInstance_;           // 应用程序实例句柄
    HWND hMainWnd_;                // 主窗口句柄
    HWND hStatusBar_;              // 状态栏句柄
    
    // 游戏相关成员变量
    std::unique_ptr<GameController> gameController_;  // 游戏控制器（智能指针管理）
    
    // 界面布局常量
    static const int BOARD_SIZE = 15;        // 棋盘大小
    static const int CELL_SIZE = 30;         // 每个格子的像素大小
    static const int BOARD_MARGIN = 50;      // 棋盘边距
    static const int STONE_RADIUS = 12;      // 棋子半径
    static const int WINDOW_WIDTH = BOARD_SIZE * CELL_SIZE + BOARD_MARGIN * 2 + 20;
    static const int WINDOW_HEIGHT = BOARD_SIZE * CELL_SIZE + BOARD_MARGIN * 2 + 120;
    
    // 菜单ID常量
    static const int ID_NEW_GAME = 1001;     // 新游戏菜单ID
    static const int ID_RESTART = 1002;      // 重新开始菜单ID
    static const int ID_RANK_LIST = 1003;    // 排行榜菜单ID
    static const int ID_GAME_RULES = 1004;   // 游戏规则菜单ID
    static const int ID_ABOUT = 1005;        // 关于菜单ID
    static const int ID_EXIT = 1006;         // 退出菜单ID
    
    // 私有方法声明
    
    /**
     * @brief 注册窗口类
     * @return 如果注册成功返回true
     * 
     * 这个方法向Windows系统注册我们的窗口类，
     * 定义窗口的基本属性和消息处理函数。
     */
    bool RegisterWindowClass();
    
    /**
     * @brief 创建主窗口
     * @return 如果创建成功返回true
     * 
     * 创建应用程序的主窗口，设置窗口大小、位置等属性。
     */
    bool CreateMainWindow();
    
    /**
     * @brief 创建菜单栏
     * 
     * 创建应用程序的菜单栏，包括游戏菜单和帮助菜单。
     */
    void CreateMenuBar();
    
    /**
     * @brief 创建状态栏
     * 
     * 创建窗口底部的状态栏，用于显示游戏状态信息。
     */
    void CreateStatusBar();
    
    /**
     * @brief 绘制棋盘
     * @param hdc 设备上下文句柄
     * 
     * 在指定的设备上下文上绘制15x15的棋盘网格，
     * 包括网格线、天元点、星位等。
     */
    void DrawBoard(HDC hdc);
    
    /**
     * @brief 绘制棋子
     * @param hdc 设备上下文句柄
     * @param row 行坐标
     * @param col 列坐标
     * @param pieceType 棋子类型
     * 
     * 在指定位置绘制黑子或白子。
     */
    void DrawPiece(HDC hdc, int row, int col, PieceType pieceType);
    
    /**
     * @brief 绘制所有棋子
     * @param hdc 设备上下文句柄
     * 
     * 遍历棋盘，绘制所有已下的棋子。
     */
    void DrawAllPieces(HDC hdc);
    
    /**
     * @brief 高亮显示最后一步棋
     * @param hdc 设备上下文句柄
     * 
     * 在最后下的棋子周围绘制高亮标记。
     */
    void HighlightLastMove(HDC hdc);
    
    /**
     * @brief 将屏幕坐标转换为棋盘坐标
     * @param x 屏幕X坐标
     * @param y 屏幕Y坐标
     * @param row 输出参数：棋盘行坐标
     * @param col 输出参数：棋盘列坐标
     * @return 如果转换成功返回true
     * 
     * 将鼠标点击的屏幕坐标转换为棋盘上的行列坐标。
     */
    bool ScreenToBoardCoords(int x, int y, int& row, int& col);
    
    /**
     * @brief 将棋盘坐标转换为屏幕坐标
     * @param row 棋盘行坐标
     * @param col 棋盘列坐标
     * @param x 输出参数：屏幕X坐标
     * @param y 输出参数：屏幕Y坐标
     * 
     * 将棋盘坐标转换为屏幕绘图坐标。
     */
    void BoardToScreenCoords(int row, int col, int& x, int& y);
    
    /**
     * @brief 处理鼠标左键点击事件
     * @param x 鼠标X坐标
     * @param y 鼠标Y坐标
     * 
     * 处理用户的下棋操作。
     */
    void HandleMouseClick(int x, int y);
    
    /**
     * @brief 处理菜单命令
     * @param menuId 菜单项ID
     * 
     * 处理用户选择的菜单命令。
     */
    void HandleMenuCommand(int menuId);
    
    /**
     * @brief 更新状态栏显示
     * 
     * 根据当前游戏状态更新状态栏的文本显示。
     */
    void UpdateStatusBar();
    
    /**
     * @brief 显示游戏结束对话框
     * @param result 游戏结果
     * 
     * 当游戏结束时显示结果对话框，并处理排行榜相关操作。
     */
    void ShowGameEndDialog(GameResult result);
    
    /**
     * @brief 显示排行榜对话框
     * 
     * 显示当前的排行榜信息。
     */
    void ShowRankDialog();
    
    /**
     * @brief 显示游戏规则对话框
     * 
     * 显示五子棋游戏的规则说明。
     */
    void ShowRulesDialog();
    
    /**
     * @brief 显示关于对话框
     * 
     * 显示程序的版本信息和作者信息。
     */
    void ShowAboutDialog();
    
    /**
     * @brief 获取用户输入的姓名
     * @param playerName 输出参数：用户输入的姓名
     * @return 如果用户确认输入返回true
     * 
     * 弹出输入对话框，让用户输入姓名用于排行榜记录。
     */
    bool GetPlayerName(std::string& playerName);

public:
    /**
     * @brief 构造函数
     * 
     * 初始化成员变量为默认值。
     */
    GomokuApp();
    
    /**
     * @brief 析构函数
     * 
     * 清理资源，确保程序正常退出。
     */
    ~GomokuApp();
    
    /**
     * @brief 初始化应用程序
     * @param hInstance 应用程序实例句柄
     * @return 如果初始化成功返回true
     * 
     * 这个方法必须在运行应用程序之前调用，
     * 它负责初始化所有必要的组件。
     */
    bool Initialize(HINSTANCE hInstance);
    
    /**
     * @brief 运行应用程序
     * @return 返回程序退出代码
     * 
     * 启动消息循环，开始处理Windows消息。
     * 这个方法会一直运行直到用户关闭程序。
     */
    int Run();
    
    /**
     * @brief 静态窗口过程函数
     * @param hwnd 窗口句柄
     * @param uMsg 消息类型
     * @param wParam 消息参数1
     * @param lParam 消息参数2
     * @return 消息处理结果
     * 
     * 这是Windows消息处理的入口点，
     * 它将消息转发给相应的GomokuApp实例。
     */
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    
    /**
     * @brief 实例窗口过程函数
     * @param hwnd 窗口句柄
     * @param uMsg 消息类型
     * @param wParam 消息参数1
     * @param lParam 消息参数2
     * @return 消息处理结果
     * 
     * 实际的消息处理函数，处理各种Windows消息。
     */
    LRESULT HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
};

#endif // GOMOKUAPP_H
