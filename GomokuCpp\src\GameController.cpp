/**
 * @file GameController.cpp
 * @brief GameController类的实现文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件实现了GameController类的所有方法。
 * GameController是整个五子棋游戏的控制中心。
 */

#include "GameController.h"
#include <sstream>
#include <stdexcept>

/**
 * @brief 私有构造函数实现
 * 
 * 单例模式的实现，初始化成员变量。
 */
GameController::GameController() 
    : currentState_(GameState::MENU), 
      lastMessage_("欢迎来到五子棋游戏！"), 
      gameInitialized_(false) {
    // 构造函数中不创建对象，延迟到initialize方法中
}

/**
 * @brief 析构函数实现
 * 
 * 确保程序结束时保存数据。
 */
GameController::~GameController() {
    if (gameInitialized_) {
        saveRankData();  // 保存排行榜数据
    }
}

/**
 * @brief 获取单例实例
 * 
 * @return 返回GameController的唯一实例
 * 
 * 使用局部静态变量实现线程安全的单例模式。
 * 这是C++11推荐的单例实现方式。
 */
GameController& GameController::getInstance() {
    static GameController instance;  // 局部静态变量，程序结束时自动销毁
    return instance;
}

/**
 * @brief 初始化游戏系统
 * 
 * @param rankFilename 排行榜文件名
 * @return 如果初始化成功返回true
 * 
 * 这个方法创建游戏所需的所有组件，并加载数据。
 */
bool GameController::initialize(const std::string& rankFilename) {
    try {
        // 创建游戏棋盘
        gameBoard_ = std::make_unique<GameBoard>();
        
        // 创建排行榜管理器
        rankManager_ = std::make_unique<RankManager>(rankFilename);
        
        // 加载排行榜数据
        rankManager_->loadFromFile();
        
        // 设置初始状态
        currentState_ = GameState::MENU;
        lastMessage_ = "游戏初始化成功！";
        gameInitialized_ = true;
        
        return true;
    } catch (const std::exception& e) {
        lastMessage_ = "游戏初始化失败：" + std::string(e.what());
        return false;
    }
}

/**
 * @brief 开始新游戏
 * 
 * 重置游戏棋盘，设置游戏状态为进行中。
 */
void GameController::startNewGame() {
    if (!gameInitialized_) {
        lastMessage_ = "游戏未初始化！";
        return;
    }
    
    // 重置棋盘
    gameBoard_->reset();
    
    // 设置游戏状态
    currentState_ = GameState::PLAYING;
    lastMessage_ = "新游戏开始！黑子先行。";
}

/**
 * @brief 在指定位置下棋
 * 
 * @param row 行坐标 (0-14)
 * @param col 列坐标 (0-14)
 * @return 如果下棋成功返回true
 * 
 * 这是游戏的核心交互方法，处理玩家的下棋操作。
 */
bool GameController::makeMove(int row, int col) {
    if (!gameInitialized_) {
        lastMessage_ = "游戏未初始化！";
        return false;
    }
    
    if (currentState_ != GameState::PLAYING) {
        lastMessage_ = "游戏未在进行中！";
        return false;
    }
    
    // 记录当前玩家（下棋前）
    PieceType currentPlayer = gameBoard_->getCurrentPlayer();
    
    // 尝试下棋
    if (!gameBoard_->makeMove(row, col)) {
        lastMessage_ = "无效的下棋位置！";
        return false;
    }
    
    // 更新消息
    std::ostringstream oss;
    if (currentPlayer == PieceType::BLACK) {
        oss << "黑子下在 (" << row << ", " << col << ")";
    } else {
        oss << "白子下在 (" << row << ", " << col << ")";
    }
    lastMessage_ = oss.str();
    
    // 检查游戏是否结束
    GameResult result = gameBoard_->checkGameResult();
    if (result != GameResult::CONTINUE) {
        handleGameEnd(result);
    }
    
    return true;
}

/**
 * @brief 在指定位置下棋（Point版本）
 * 
 * @param point 下棋位置
 * @return 如果下棋成功返回true
 */
bool GameController::makeMove(const Point& point) {
    return makeMove(point.getRow(), point.getCol());
}

/**
 * @brief 检查指定位置是否可以下棋
 * 
 * @param row 行坐标
 * @param col 列坐标
 * @return 如果可以下棋返回true
 */
bool GameController::isValidMove(int row, int col) const {
    if (!gameInitialized_ || currentState_ != GameState::PLAYING) {
        return false;
    }
    
    return gameBoard_->isValidMove(row, col);
}

/**
 * @brief 处理游戏结束逻辑
 * 
 * @param result 游戏结果
 * 
 * 当游戏结束时调用，处理获胜、平局等情况。
 */
void GameController::handleGameEnd(GameResult result) {
    currentState_ = GameState::GAME_OVER;
    
    switch (result) {
        case GameResult::BLACK_WIN:
            lastMessage_ = "游戏结束！黑子获胜！";
            break;
        case GameResult::WHITE_WIN:
            lastMessage_ = "游戏结束！白子获胜！";
            break;
        case GameResult::DRAW:
            lastMessage_ = "游戏结束！平局！";
            break;
        default:
            lastMessage_ = "游戏结束！";
            break;
    }
    
    // 如果有获胜者，检查是否能进入排行榜
    if (result == GameResult::BLACK_WIN || result == GameResult::WHITE_WIN) {
        int steps = gameBoard_->getTotalMoves();
        if (rankManager_->canEnterRank(steps)) {
            int position = rankManager_->getRankPosition(steps);
            std::ostringstream oss;
            oss << lastMessage_ << " 恭喜！您的成绩可以进入排行榜第 " << position << " 位！";
            lastMessage_ = oss.str();
        }
    }
}

/**
 * @brief 获取当前游戏状态
 * 
 * @return 返回当前游戏状态枚举值
 */
GameState GameController::getCurrentState() const {
    return currentState_;
}

/**
 * @brief 获取游戏结果
 * 
 * @return 返回当前游戏结果
 */
GameResult GameController::getGameResult() const {
    if (!gameInitialized_) {
        return GameResult::CONTINUE;
    }
    return gameBoard_->checkGameResult();
}

/**
 * @brief 获取当前玩家
 * 
 * @return 返回当前应该下棋的玩家
 */
PieceType GameController::getCurrentPlayer() const {
    if (!gameInitialized_) {
        return PieceType::BLACK;
    }
    return gameBoard_->getCurrentPlayer();
}

/**
 * @brief 获取指定位置的棋子类型
 * 
 * @param row 行坐标
 * @param col 列坐标
 * @return 返回该位置的棋子类型
 */
PieceType GameController::getPiece(int row, int col) const {
    if (!gameInitialized_) {
        return PieceType::EMPTY;
    }
    return gameBoard_->getPiece(row, col);
}

/**
 * @brief 获取总步数
 * 
 * @return 返回已下棋子的总数
 */
int GameController::getTotalMoves() const {
    if (!gameInitialized_) {
        return 0;
    }
    return gameBoard_->getTotalMoves();
}

/**
 * @brief 获取最后一步棋的位置
 * 
 * @return 返回最后下棋的位置
 */
Point GameController::getLastMove() const {
    if (!gameInitialized_) {
        return Point(0, 0);
    }
    return gameBoard_->getLastMove();
}

/**
 * @brief 获取棋盘的字符串表示
 * 
 * @return 返回棋盘的字符串表示
 */
std::string GameController::getBoardString() const {
    if (!gameInitialized_) {
        return "游戏未初始化";
    }
    return gameBoard_->toString();
}

/**
 * @brief 添加排行榜记录
 *
 * @param playerName 玩家姓名
 * @param steps 获胜步数
 * @return 如果添加成功返回true
 */
bool GameController::addRankRecord(const std::string& playerName, int steps) {
    if (!gameInitialized_) {
        lastMessage_ = "游戏未初始化！";
        return false;
    }

    bool success = rankManager_->addRecord(playerName, steps);
    if (success) {
        rankManager_->saveToFile();  // 立即保存到文件
        lastMessage_ = "排行榜记录添加成功！";
    } else {
        lastMessage_ = "排行榜记录添加失败！";
    }

    return success;
}

/**
 * @brief 检查指定步数是否能进入排行榜
 *
 * @param steps 要检查的步数
 * @return 如果能进入排行榜返回true
 */
bool GameController::canEnterRank(int steps) const {
    if (!gameInitialized_) {
        return false;
    }
    return rankManager_->canEnterRank(steps);
}

/**
 * @brief 获取指定步数的排名
 *
 * @param steps 要查询的步数
 * @return 返回排名（1-based），如果不能进入返回-1
 */
int GameController::getRankPosition(int steps) const {
    if (!gameInitialized_) {
        return -1;
    }
    return rankManager_->getRankPosition(steps);
}

/**
 * @brief 获取排行榜字符串
 *
 * @return 返回格式化的排行榜字符串
 */
std::string GameController::getRankString() const {
    if (!gameInitialized_) {
        return "游戏未初始化";
    }
    return rankManager_->toString();
}

/**
 * @brief 获取排行榜记录数量
 *
 * @return 返回当前记录数量
 */
int GameController::getRankCount() const {
    if (!gameInitialized_) {
        return 0;
    }
    return rankManager_->getRecordCount();
}

/**
 * @brief 获取指定位置的排行榜记录
 *
 * @param index 记录索引 (0-based)
 * @return 返回记录的引用
 */
const RankRecord& GameController::getRankRecord(int index) const {
    if (!gameInitialized_) {
        throw std::runtime_error("游戏未初始化");
    }
    return rankManager_->getRecord(index);
}

/**
 * @brief 保存排行榜数据
 *
 * @return 如果保存成功返回true
 */
bool GameController::saveRankData() {
    if (!gameInitialized_) {
        return false;
    }
    return rankManager_->saveToFile();
}

/**
 * @brief 加载排行榜数据
 *
 * @return 如果加载成功返回true
 */
bool GameController::loadRankData() {
    if (!gameInitialized_) {
        return false;
    }
    return rankManager_->loadFromFile();
}

/**
 * @brief 设置游戏状态
 *
 * @param state 新的游戏状态
 */
void GameController::setState(GameState state) {
    currentState_ = state;
}

/**
 * @brief 获取最后的消息
 *
 * @return 返回最后的消息字符串
 */
std::string GameController::getLastMessage() const {
    return lastMessage_;
}

/**
 * @brief 设置消息
 *
 * @param message 要设置的消息
 */
void GameController::setMessage(const std::string& message) {
    lastMessage_ = message;
}

/**
 * @brief 获取游戏规则字符串
 *
 * @return 返回游戏规则的详细说明
 *
 * 这个方法返回五子棋游戏的完整规则说明，
 * 可以用于帮助界面或新手指导。
 */
std::string GameController::getGameRules() const {
    std::ostringstream oss;

    oss << "==================== 五子棋游戏规则 ====================\n\n";

    oss << "【游戏目标】\n";
    oss << "在15×15的棋盘上，率先在横、竖、斜任一方向连成5子者获胜。\n\n";

    oss << "【基本规则】\n";
    oss << "1. 游戏双方分别使用黑白两色棋子\n";
    oss << "2. 黑子先行，白子后行\n";
    oss << "3. 双方轮流在棋盘交叉点上下子\n";
    oss << "4. 棋子下定后不能移动或悔棋\n";
    oss << "5. 率先形成五子连珠者获胜\n\n";

    oss << "【获胜条件】\n";
    oss << "在以下任一方向连成5个同色棋子：\n";
    oss << "• 水平方向（横向）\n";
    oss << "• 垂直方向（纵向）\n";
    oss << "• 主对角线方向（\\）\n";
    oss << "• 副对角线方向（/）\n\n";

    oss << "【平局条件】\n";
    oss << "棋盘下满（225手）仍无人获胜则为平局。\n\n";

    oss << "【操作说明】\n";
    oss << "• 控制台版：输入坐标如 A0, B5, H7 等\n";
    oss << "• GUI版：鼠标点击棋盘交叉点\n";
    oss << "• 坐标系：行用数字0-14表示，列用字母A-O表示\n\n";

    oss << "【排行榜】\n";
    oss << "系统记录获胜玩家的步数，步数越少排名越高。\n";
    oss << "最多保存前10名的记录。\n\n";

    oss << "=====================================================\n";

    return oss.str();
}

/**
 * @brief 重置游戏
 *
 * 重置游戏状态，但保留排行榜数据。
 */
void GameController::resetGame() {
    if (!gameInitialized_) {
        lastMessage_ = "游戏未初始化！";
        return;
    }

    // 重置棋盘
    gameBoard_->reset();

    // 重置状态
    currentState_ = GameState::MENU;
    lastMessage_ = "游戏已重置。";
}

/**
 * @brief 退出游戏
 *
 * 保存数据并设置退出状态。
 */
void GameController::exitGame() {
    if (gameInitialized_) {
        // 保存排行榜数据
        saveRankData();
        lastMessage_ = "数据已保存，感谢游戏！";
    }

    // 设置退出状态
    currentState_ = GameState::EXIT;
}
