/**
 * @file ConsoleMain.cpp
 * @brief 五子棋游戏控制台版本的主程序
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件实现了五子棋游戏的控制台用户界面。
 * 它使用GameController来管理游戏逻辑，提供文本界面供用户交互。
 * 
 * 主要功能：
 * - 显示主菜单
 * - 处理用户输入
 * - 显示游戏棋盘
 * - 显示排行榜
 * - 显示游戏规则
 */

#include "GameController.h"
#include <iostream>
#include <string>
#include <cctype>
#include <limits>

/**
 * @brief 控制台游戏类
 * 
 * 这个类负责处理控制台界面的显示和用户交互。
 * 它是View层的实现，与GameController（Controller层）配合工作。
 */
class ConsoleGame {
private:
    GameController& controller_;  // 游戏控制器引用
    
    /**
     * @brief 清屏函数
     * 
     * 跨平台的清屏实现。
     */
    void clearScreen() {
        #ifdef _WIN32
            system("cls");
        #else
            system("clear");
        #endif
    }
    
    /**
     * @brief 暂停等待用户按键
     */
    void pauseForUser() {
        std::cout << "\n按回车键继续...";
        std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
        std::cin.get();
    }
    
    /**
     * @brief 显示游戏标题
     */
    void showTitle() {
        std::cout << "╔══════════════════════════════════════╗\n";
        std::cout << "║            五子棋游戏 C++版           ║\n";
        std::cout << "║         Gomoku Game (C++ Version)    ║\n";
        std::cout << "╚══════════════════════════════════════╝\n\n";
    }
    
    /**
     * @brief 显示主菜单
     */
    void showMainMenu() {
        std::cout << "┌─────────────── 主菜单 ───────────────┐\n";
        std::cout << "│  1. 开始新游戏                       │\n";
        std::cout << "│  2. 查看排行榜                       │\n";
        std::cout << "│  3. 游戏规则                         │\n";
        std::cout << "│  4. 退出游戏                         │\n";
        std::cout << "└─────────────────────────────────────┘\n";
        std::cout << "请选择 (1-4): ";
    }
    
    /**
     * @brief 显示当前游戏状态
     */
    void showGameStatus() {
        std::cout << "\n当前玩家: ";
        if (controller_.getCurrentPlayer() == PieceType::BLACK) {
            std::cout << "黑子 (●)";
        } else {
            std::cout << "白子 (○)";
        }
        std::cout << "  |  总步数: " << controller_.getTotalMoves() << "\n";
        std::cout << "消息: " << controller_.getLastMessage() << "\n\n";
    }
    
    /**
     * @brief 解析用户输入的坐标
     * @param input 用户输入的字符串
     * @param row 输出参数：行坐标
     * @param col 输出参数：列坐标
     * @return 如果解析成功返回true
     * 
     * 支持的输入格式：
     * - A0, B5, H7 等（字母+数字）
     * - a0, b5, h7 等（小写字母+数字）
     */
    bool parseCoordinate(const std::string& input, int& row, int& col) {
        if (input.length() < 2) {
            return false;
        }
        
        // 解析列坐标（字母）
        char colChar = std::toupper(input[0]);
        if (colChar < 'A' || colChar > 'O') {
            return false;
        }
        col = colChar - 'A';
        
        // 解析行坐标（数字）
        try {
            std::string rowStr = input.substr(1);
            row = std::stoi(rowStr);
            if (row < 0 || row > 14) {
                return false;
            }
        } catch (const std::exception&) {
            return false;
        }
        
        return true;
    }
    
    /**
     * @brief 处理游戏进行中的用户输入
     */
    void handleGameInput() {
        std::string input;
        std::cout << "请输入坐标 (如 A0, B5, H7) 或输入 'quit' 返回主菜单: ";
        std::cin >> input;
        
        // 检查是否要退出
        if (input == "quit" || input == "QUIT") {
            controller_.setState(GameState::MENU);
            return;
        }
        
        // 解析坐标
        int row, col;
        if (!parseCoordinate(input, row, col)) {
            controller_.setMessage("无效的坐标格式！请使用如 A0, B5, H7 的格式。");
            return;
        }
        
        // 尝试下棋
        if (!controller_.makeMove(row, col)) {
            // 错误消息已在makeMove中设置
            return;
        }
        
        // 检查游戏是否结束
        GameResult result = controller_.getGameResult();
        if (result != GameResult::CONTINUE) {
            handleGameEnd(result);
        }
    }
    
    /**
     * @brief 处理游戏结束
     * @param result 游戏结果
     */
    void handleGameEnd(GameResult result) {
        clearScreen();
        showTitle();
        std::cout << controller_.getBoardString() << "\n";
        
        // 显示结果
        std::cout << controller_.getLastMessage() << "\n\n";
        
        // 如果有获胜者且能进入排行榜，询问玩家姓名
        if ((result == GameResult::BLACK_WIN || result == GameResult::WHITE_WIN) &&
            controller_.canEnterRank(controller_.getTotalMoves())) {
            
            std::string playerName;
            std::cout << "请输入您的姓名以记录到排行榜: ";
            std::cin.ignore();  // 清除输入缓冲区
            std::getline(std::cin, playerName);
            
            if (!playerName.empty()) {
                controller_.addRankRecord(playerName, controller_.getTotalMoves());
                std::cout << "恭喜！您的成绩已记录到排行榜！\n";
            }
        }
        
        pauseForUser();
        controller_.setState(GameState::MENU);
    }

public:
    /**
     * @brief 构造函数
     * @param ctrl 游戏控制器引用
     */
    explicit ConsoleGame(GameController& ctrl) : controller_(ctrl) {}
    
    /**
     * @brief 运行游戏主循环
     */
    void run() {
        // 初始化游戏
        if (!controller_.initialize()) {
            std::cout << "游戏初始化失败！\n";
            return;
        }
        
        // 主游戏循环
        while (controller_.getCurrentState() != GameState::EXIT) {
            switch (controller_.getCurrentState()) {
                case GameState::MENU:
                    handleMenu();
                    break;
                case GameState::PLAYING:
                    handlePlaying();
                    break;
                case GameState::SHOW_RANK:
                    handleShowRank();
                    break;
                case GameState::SHOW_RULES:
                    handleShowRules();
                    break;
                default:
                    controller_.setState(GameState::MENU);
                    break;
            }
        }
        
        std::cout << "感谢游戏！再见！\n";
    }
    
    /**
     * @brief 处理主菜单
     */
    void handleMenu() {
        clearScreen();
        showTitle();
        showMainMenu();
        
        int choice;
        std::cin >> choice;
        
        switch (choice) {
            case 1:
                controller_.startNewGame();
                break;
            case 2:
                controller_.setState(GameState::SHOW_RANK);
                break;
            case 3:
                controller_.setState(GameState::SHOW_RULES);
                break;
            case 4:
                controller_.exitGame();
                break;
            default:
                controller_.setMessage("无效的选择，请输入 1-4！");
                pauseForUser();
                break;
        }
    }
    
    /**
     * @brief 处理游戏进行状态
     */
    void handlePlaying() {
        clearScreen();
        showTitle();
        showGameStatus();
        std::cout << controller_.getBoardString() << "\n";
        handleGameInput();
    }
    
    /**
     * @brief 处理显示排行榜
     */
    void handleShowRank() {
        clearScreen();
        showTitle();
        std::cout << controller_.getRankString() << "\n";
        pauseForUser();
        controller_.setState(GameState::MENU);
    }
    
    /**
     * @brief 处理显示游戏规则
     */
    void handleShowRules() {
        clearScreen();
        showTitle();
        std::cout << controller_.getGameRules() << "\n";
        pauseForUser();
        controller_.setState(GameState::MENU);
    }
};

/**
 * @brief 主函数
 * 
 * 程序的入口点，创建游戏对象并运行。
 */
int main() {
    try {
        // 获取游戏控制器实例
        GameController& gameController = GameController::getInstance();
        
        // 创建控制台游戏界面
        ConsoleGame consoleGame(gameController);
        
        // 运行游戏
        consoleGame.run();
        
    } catch (const std::exception& e) {
        std::cerr << "程序运行出错: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
