@echo off
echo 正在编译五子棋GUI版本...

REM 设置Visual Studio环境变量
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat" 2>nul
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat" 2>nul
)
if errorlevel 1 (
    echo 错误: 找不到Visual Studio 2019
    echo 请确保已安装Visual Studio 2019
    pause
    exit /b 1
)

REM 编译源文件
cl /Fe:GomokuGUI.exe ^
   /I. ^
   /DWIN32 /D_WINDOWS ^
   winmain.c game.c rank_service.c rank_file.c ^
   user32.lib gdi32.lib comctl32.lib

if errorlevel 1 (
    echo 编译失败!
    pause
    exit /b 1
)

echo 编译成功! 生成了 GomokuGUI.exe
echo 可以运行 GomokuGUI.exe 来启动游戏
pause
