# 五子棋游戏C++版本 - 学生文档

## 项目概述

这是一个使用C++和Win32 API开发的五子棋游戏，采用面向对象设计，具有完整的图形用户界面。项目适合作为C++编程课程的实践项目，涵盖了面向对象编程、Windows GUI开发、游戏逻辑设计等多个知识点。

## 技术栈

- **编程语言**: C++17
- **开发环境**: Visual Studio 2019/2022
- **界面框架**: Win32 API
- **设计模式**: MVC (Model-View-Controller)
- **内存管理**: 智能指针 (std::unique_ptr)

## 项目结构

```
GomokuCpp/
├── src/                    # 源代码目录
│   ├── main.cpp           # 程序入口
│   ├── GomokuApp.h/cpp    # 主应用程序类
│   ├── GameController.h/cpp # 游戏控制器
│   ├── GameBoard.h/cpp    # 游戏棋盘逻辑
│   ├── RankManager.h/cpp  # 排行榜管理
│   └── Point.h/cpp        # 基础数据结构
├── GomokuCpp.sln          # Visual Studio解决方案
├── GomokuCpp.vcxproj      # Visual Studio项目文件
├── CMakeLists.txt         # CMake构建文件
└── 学生文档.md            # 本文档
```

## 核心类设计

### 1. Point类 - 基础数据结构
```cpp
class Point {
private:
    int row_, col_;        // 坐标
    PieceType piece_;      // 棋子类型
public:
    // 构造函数、getter/setter方法
    // 坐标验证、比较运算符等
};
```

**学习要点**:
- 封装性：私有成员变量，公有接口
- 构造函数重载
- 运算符重载
- const成员函数

### 2. GameBoard类 - 游戏逻辑核心
```cpp
class GameBoard {
private:
    std::vector<std::vector<PieceType>> board_;  // 棋盘状态
    PieceType currentPlayer_;                    // 当前玩家
    
    // 私有辅助方法
    bool checkHorizontal(const Point& point) const;
    bool checkVertical(const Point& point) const;
    // ...
    
public:
    bool makeMove(int row, int col);            // 下棋
    GameResult checkGameResult() const;         // 检查游戏结果
    // ...
};
```

**学习要点**:
- STL容器的使用 (std::vector)
- 算法设计：五子连珠判断
- 状态管理
- 接口设计

### 3. RankManager类 - 数据管理
```cpp
class RankManager {
private:
    std::vector<RankRecord> records_;
    std::string filename_;
    
    void sortRecords();                         // 排序
    std::string getCurrentDate() const;         // 获取日期
    
public:
    bool addRecord(const std::string& name, int steps);
    bool loadFromFile(const std::string& filename);
    bool saveToFile(const std::string& filename);
    // ...
};
```

**学习要点**:
- 文件I/O操作
- 数据排序算法
- 异常处理
- RAII原则

### 4. GameController类 - 控制器
```cpp
class GameController {
private:
    std::unique_ptr<GameBoard> gameBoard_;
    std::unique_ptr<RankManager> rankManager_;
    GameState currentState_;
    
    // 单例模式实现
    GameController();
    ~GameController();
    GameController(const GameController&) = delete;
    GameController& operator=(const GameController&) = delete;
    
public:
    static GameController& getInstance();       // 单例获取
    bool initialize(const std::string& filename);
    bool makeMove(int row, int col);
    // ...
};
```

**学习要点**:
- 单例设计模式
- 智能指针使用
- 组合关系
- 删除函数 (= delete)

### 5. GomokuApp类 - GUI界面
```cpp
class GomokuApp {
private:
    HINSTANCE hInstance_;
    HWND hMainWnd_, hStatusBar_;
    std::unique_ptr<GameController> gameController_;
    
    // Win32 API相关方法
    bool RegisterWindowClass();
    bool CreateMainWindow();
    void DrawBoard(HDC hdc);
    void HandleMouseClick(int x, int y);
    
public:
    bool Initialize(HINSTANCE hInstance);
    int Run();
    static LRESULT CALLBACK WindowProc(HWND, UINT, WPARAM, LPARAM);
    LRESULT HandleMessage(HWND, UINT, WPARAM, LPARAM);
};
```

**学习要点**:
- Win32 API编程
- 事件驱动编程
- 图形绘制
- 消息处理机制

## 关键算法解析

### 1. 五子连珠判断算法

```cpp
bool GameBoard::checkHorizontal(const Point& point) const {
    int count = 1;  // 包括当前位置
    PieceType piece = point.getPiece();
    int row = point.getRow();
    int col = point.getCol();
    
    // 向左检查
    for (int c = col - 1; c >= 0 && board_[row][c] == piece; --c) {
        count++;
    }
    
    // 向右检查
    for (int c = col + 1; c < BOARD_SIZE && board_[row][c] == piece; ++c) {
        count++;
    }
    
    return count >= 5;  // 五子连珠
}
```

**算法思路**:
1. 从当前位置开始，向两个方向扩展
2. 计算连续相同棋子的数量
3. 四个方向分别检查：水平、垂直、两条对角线

### 2. 坐标转换算法

```cpp
bool GomokuApp::ScreenToBoardCoords(int x, int y, int& row, int& col) {
    // 计算相对于棋盘左上角的坐标
    int relativeX = x - BOARD_MARGIN;
    int relativeY = y - BOARD_MARGIN;
    
    // 转换为棋盘坐标（四舍五入到最近的交叉点）
    col = (relativeX + CELL_SIZE / 2) / CELL_SIZE;
    row = (relativeY + CELL_SIZE / 2) / CELL_SIZE;
    
    // 检查坐标是否在有效范围内
    return (row >= 0 && row < BOARD_SIZE && col >= 0 && col < BOARD_SIZE);
}
```

**算法思路**:
1. 将屏幕像素坐标转换为棋盘逻辑坐标
2. 使用四舍五入实现就近对齐
3. 边界检查确保坐标有效

## 设计模式应用

### 1. 单例模式 (Singleton)
```cpp
GameController& GameController::getInstance() {
    static GameController instance;  // 线程安全的单例
    return instance;
}
```

**应用场景**: 确保游戏控制器全局唯一

### 2. MVC模式
- **Model**: GameBoard, RankManager (数据和逻辑)
- **View**: GomokuApp (用户界面)
- **Controller**: GameController (控制逻辑)

**优势**: 分离关注点，提高代码可维护性

### 3. RAII (Resource Acquisition Is Initialization)
```cpp
std::unique_ptr<GameBoard> gameBoard_;  // 自动内存管理
```

**应用**: 使用智能指针自动管理内存

## 编程技巧和最佳实践

### 1. 异常安全
```cpp
try {
    gameController_ = std::make_unique<GameController>();
    // ...
} catch (const std::exception& e) {
    // 错误处理
    return false;
}
```

### 2. const正确性
```cpp
int getRow() const;                    // 不修改对象状态
bool isValid() const;                  // const成员函数
const std::vector<RankRecord>& getAllRecords() const;  // 返回const引用
```

### 3. 初始化列表
```cpp
Point::Point(int row, int col, PieceType piece) 
    : row_(row), col_(col), piece_(piece) {
    // 使用初始化列表，更高效
}
```

### 4. 智能指针
```cpp
std::unique_ptr<GameBoard> gameBoard_;     // 独占所有权
std::make_unique<GameBoard>();             // 安全创建
```

## 编译和运行

### 方法1: Visual Studio
1. 双击 `GomokuCpp.sln` 打开项目
2. 选择 Release 或 Debug 配置
3. 按 F5 编译并运行

### 方法2: CMake
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 16 2019" -A Win32
cmake --build . --config Release
```

### 方法3: 命令行编译
```bash
cl /EHsc /std:c++17 /Fe:GomokuGame.exe src/*.cpp user32.lib gdi32.lib comctl32.lib
```

## 功能特性

### 游戏功能
- ✅ 标准15×15五子棋规则
- ✅ 图形化用户界面
- ✅ 鼠标点击下棋
- ✅ 实时胜负判断
- ✅ 游戏状态显示

### 界面功能
- ✅ 菜单栏操作
- ✅ 状态栏信息显示
- ✅ 最后一步高亮
- ✅ 键盘快捷键支持

### 数据功能
- ✅ 排行榜系统
- ✅ 数据持久化存储
- ✅ 成绩排序

## 扩展建议

### 初级扩展
1. 添加悔棋功能
2. 添加游戏计时器
3. 美化界面样式
4. 添加音效

### 中级扩展
1. 实现简单AI对手
2. 添加游戏回放功能
3. 支持自定义棋盘大小
4. 网络对战功能

### 高级扩展
1. 实现高级AI算法 (Alpha-Beta剪枝)
2. 添加开局库
3. 实现禁手规则
4. 制作安装包

## 学习收获

通过这个项目，学生可以掌握：

1. **C++核心概念**
   - 面向对象编程
   - STL容器和算法
   - 智能指针
   - 异常处理

2. **设计模式**
   - 单例模式
   - MVC模式
   - RAII原则

3. **Windows编程**
   - Win32 API基础
   - 事件驱动编程
   - 图形绘制

4. **软件工程**
   - 项目结构设计
   - 代码注释规范
   - 版本控制

5. **算法设计**
   - 游戏逻辑算法
   - 数据排序
   - 坐标转换

## 常见问题

### Q: 编译时出现链接错误？
A: 确保链接了必要的Windows库：user32.lib, gdi32.lib, comctl32.lib

### Q: 程序运行时崩溃？
A: 检查指针是否为空，确保在使用前进行初始化

### Q: 中文显示乱码？
A: 确保项目设置为Unicode字符集，使用宽字符串

### Q: 如何调试程序？
A: 使用Visual Studio的调试器，设置断点，单步执行

## 总结

这个五子棋项目是一个很好的C++学习实践，它涵盖了从基础语法到高级特性的各个方面。通过完成这个项目，学生不仅能掌握C++编程技能，还能了解软件开发的完整流程。

项目代码结构清晰，注释详细，适合作为教学案例。同时，项目具有很好的扩展性，学生可以在此基础上添加更多功能，进一步提升编程能力。
