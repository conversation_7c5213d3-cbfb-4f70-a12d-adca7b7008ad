/**
 * @file Point.h
 * @brief 五子棋游戏中的点和棋子数据结构定义
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件定义了五子棋游戏中使用的基本数据结构：
 * - Point类：表示棋盘上的一个位置点
 * - 棋子状态枚举：表示棋盘位置的状态（空、黑子、白子）
 */

#ifndef POINT_H
#define POINT_H

/**
 * @brief 棋子状态枚举
 * 
 * 定义棋盘上每个位置可能的状态：
 * - EMPTY: 空位置，没有棋子
 * - BLACK: 黑色棋子
 * - WHITE: 白色棋子
 */
enum class PieceType {
    EMPTY = 0,  // 空位置
    BLACK = 1,  // 黑子
    WHITE = 2   // 白子
};

/**
 * @brief 游戏结果枚举
 * 
 * 表示游戏的不同状态：
 * - CONTINUE: 游戏继续进行
 * - BLACK_WIN: 黑子获胜
 * - WHITE_WIN: 白子获胜
 * - DRAW: 平局
 */
enum class GameResult {
    CONTINUE = 0,   // 游戏继续
    BLACK_WIN = 1,  // 黑子胜利
    WHITE_WIN = 2,  // 白子胜利
    DRAW = 3        // 平局
};

/**
 * @brief 点类 - 表示棋盘上的一个位置
 * 
 * 这个类封装了棋盘上一个点的所有信息：
 * - 行坐标 (row)
 * - 列坐标 (col) 
 * - 该位置的棋子类型 (piece)
 * 
 * 使用示例：
 * Point p(7, 7, PieceType::BLACK);  // 在(7,7)位置放置黑子
 */
class Point {
private:
    int row_;        // 行坐标 (0-14)
    int col_;        // 列坐标 (0-14)
    PieceType piece_; // 该位置的棋子类型

public:
    /**
     * @brief 默认构造函数
     * 创建一个空的点，位置为(0,0)，无棋子
     */
    Point();
    
    /**
     * @brief 带参数的构造函数
     * @param row 行坐标 (0-14)
     * @param col 列坐标 (0-14)
     * @param piece 棋子类型，默认为空
     */
    Point(int row, int col, PieceType piece = PieceType::EMPTY);
    
    /**
     * @brief 拷贝构造函数
     * @param other 要拷贝的Point对象
     */
    Point(const Point& other);
    
    /**
     * @brief 赋值运算符重载
     * @param other 要赋值的Point对象
     * @return 返回当前对象的引用
     */
    Point& operator=(const Point& other);
    
    // Getter方法 - 获取私有成员变量的值
    
    /**
     * @brief 获取行坐标
     * @return 返回行坐标值 (0-14)
     */
    int getRow() const;
    
    /**
     * @brief 获取列坐标
     * @return 返回列坐标值 (0-14)
     */
    int getCol() const;
    
    /**
     * @brief 获取棋子类型
     * @return 返回该位置的棋子类型
     */
    PieceType getPiece() const;
    
    // Setter方法 - 设置私有成员变量的值
    
    /**
     * @brief 设置行坐标
     * @param row 新的行坐标值 (0-14)
     */
    void setRow(int row);
    
    /**
     * @brief 设置列坐标
     * @param col 新的列坐标值 (0-14)
     */
    void setCol(int col);
    
    /**
     * @brief 设置棋子类型
     * @param piece 新的棋子类型
     */
    void setPiece(PieceType piece);
    
    /**
     * @brief 检查坐标是否有效
     * @return 如果坐标在棋盘范围内(0-14)返回true，否则返回false
     */
    bool isValid() const;
    
    /**
     * @brief 检查该位置是否为空
     * @return 如果该位置没有棋子返回true，否则返回false
     */
    bool isEmpty() const;
    
    /**
     * @brief 等于运算符重载
     * @param other 要比较的Point对象
     * @return 如果两个点的坐标相同返回true
     */
    bool operator==(const Point& other) const;
    
    /**
     * @brief 不等于运算符重载
     * @param other 要比较的Point对象
     * @return 如果两个点的坐标不同返回true
     */
    bool operator!=(const Point& other) const;
};

#endif // POINT_H
