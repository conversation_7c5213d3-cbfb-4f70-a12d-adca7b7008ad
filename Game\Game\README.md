# 五子棋游戏 - Windows GUI版本

## 项目概述
这是一个从控制台版本转换而来的Windows GUI五子棋游戏。使用Win32 API实现图形界面，保留了原有的游戏逻辑和排行榜功能。

## 主要改进
1. **图形用户界面**: 使用Win32 API创建现代化的窗口界面
2. **鼠标操作**: 支持鼠标点击下棋，替代原来的键盘输入
3. **可视化棋盘**: 15x15网格棋盘，清晰显示黑白棋子
4. **菜单系统**: 完整的菜单栏，包含游戏控制和帮助功能
5. **状态显示**: 实时显示当前玩家和游戏状态

## 文件结构
```
Game/Game/
├── winmain.c          # 主程序文件 (新增)
├── game.c             # 游戏逻辑核心
├── game.h             # 游戏逻辑头文件
├── rank_service.c     # 排行榜服务
├── rank_service.h     # 排行榜服务头文件
├── rank_file.c        # 排行榜文件操作
├── rank_file.h        # 排行榜文件操作头文件
├── modal.h            # 数据结构定义
├── global.h           # 全局常量定义
├── Game.vcxproj       # Visual Studio项目文件 (已修改)
├── Game.sln           # Visual Studio解决方案文件
└── build.bat          # 编译脚本 (新增)
```

## 编译方法

### 方法1: 使用Visual Studio 2019
1. 双击 `Game.sln` 打开项目
2. 选择 Debug 或 Release 配置
3. 按 F5 或点击"开始调试"编译并运行

### 方法2: 使用命令行编译
1. 打开"开发者命令提示符 for VS 2019"
2. 导航到项目目录
3. 运行: `msbuild Game.vcxproj /p:Configuration=Debug /p:Platform=Win32`

### 方法3: 使用批处理脚本
1. 双击 `build.bat` 文件
2. 脚本会自动查找Visual Studio并编译项目

## 游戏功能

### 基本功能
- **新游戏**: 开始新的对局
- **重新开始**: 重置当前游戏
- **排行榜**: 查看历史最佳成绩
- **游戏规则**: 显示五子棋规则说明
- **关于**: 显示游戏信息

### 游戏规则
1. 黑子先行，白子后行
2. 轮流在棋盘交叉点下子
3. 率先在横、竖、斜任一方向连成5子者获胜
4. 棋盘填满且无人获胜则为平局

### 操作方法
- **下棋**: 鼠标左键点击棋盘交叉点
- **菜单**: 点击菜单栏选择功能
- **退出**: 点击菜单"游戏" -> "退出"

## 技术特点

### 架构设计
- **分层架构**: UI层、游戏逻辑层、数据层分离
- **模块化**: 各功能模块独立，便于维护
- **事件驱动**: 基于Windows消息机制

### 核心技术
- **Win32 API**: 原生Windows界面开发
- **GDI绘图**: 自定义棋盘和棋子绘制
- **文件I/O**: 排行榜数据持久化存储
- **内存管理**: 动态内存分配和释放

### 性能优化
- **局部重绘**: 只重绘需要更新的区域
- **资源管理**: 及时释放GDI资源
- **内存优化**: 避免内存泄漏

## 移除的控制台功能
以下控制台相关的代码已被移除或替换：
- `main.c` - 控制台主程序
- `Renju.c` - 控制台游戏入口
- `printf/scanf` - 控制台输入输出函数
- `_getch()` - 控制台字符输入
- 所有中文字符显示问题

## 系统要求
- Windows 7 或更高版本
- Visual Studio 2019 (编译需要)
- 至少 50MB 可用磁盘空间

## 故障排除

### 编译错误
1. 确保安装了Visual Studio 2019
2. 检查Windows SDK是否正确安装
3. 验证项目文件路径是否正确

### 运行错误
1. 确保所有依赖文件在同一目录
2. 检查Ranklist.dat文件权限
3. 确认系统支持Win32应用程序

## 开发说明
本项目从控制台版本完全重构为GUI版本，保持了原有的游戏逻辑完整性，同时提供了更好的用户体验。所有控制台相关代码已被移除，确保应用程序可以作为标准Windows桌面应用运行。
