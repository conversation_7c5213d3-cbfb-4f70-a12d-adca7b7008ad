/**
 * @file Point.cpp
 * @brief Point类的实现文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件实现了Point类的所有方法。
 * Point类是五子棋游戏中表示棋盘位置的基本数据结构。
 */

#include "Point.h"

// 棋盘大小常量定义
const int BOARD_SIZE = 15;  // 五子棋标准棋盘为15x15

/**
 * @brief 默认构造函数的实现
 * 
 * 创建一个位置为(0,0)的空点。
 * 这是C++中构造函数的基本用法。
 */
Point::Point() : row_(0), col_(0), piece_(PieceType::EMPTY) {
    // 使用初始化列表来初始化成员变量
    // 这种方式比在构造函数体内赋值更高效
}

/**
 * @brief 带参数构造函数的实现
 * 
 * @param row 行坐标
 * @param col 列坐标  
 * @param piece 棋子类型
 * 
 * 这个构造函数允许在创建Point对象时直接指定坐标和棋子类型。
 * 例如：Point p(7, 7, PieceType::BLACK);
 */
Point::Point(int row, int col, PieceType piece) 
    : row_(row), col_(col), piece_(piece) {
    // 同样使用初始化列表
    // 这是C++推荐的成员变量初始化方式
}

/**
 * @brief 拷贝构造函数的实现
 * 
 * @param other 要拷贝的Point对象
 * 
 * 拷贝构造函数用于创建一个对象的副本。
 * 当我们需要复制一个Point对象时会自动调用这个函数。
 */
Point::Point(const Point& other) 
    : row_(other.row_), col_(other.col_), piece_(other.piece_) {
    // 将other对象的所有成员变量复制到当前对象
}

/**
 * @brief 赋值运算符重载的实现
 * 
 * @param other 要赋值的Point对象
 * @return 返回当前对象的引用
 * 
 * 赋值运算符用于将一个已存在的对象赋值给另一个已存在的对象。
 * 例如：Point p1, p2; p1 = p2;
 */
Point& Point::operator=(const Point& other) {
    // 检查自赋值情况 (p = p)
    if (this != &other) {
        row_ = other.row_;
        col_ = other.col_;
        piece_ = other.piece_;
    }
    return *this;  // 返回当前对象的引用，支持链式赋值
}

/**
 * @brief 获取行坐标
 * 
 * @return 返回行坐标值
 * 
 * const关键字表示这个方法不会修改对象的状态，
 * 这是一个好的编程习惯。
 */
int Point::getRow() const {
    return row_;
}

/**
 * @brief 获取列坐标
 * 
 * @return 返回列坐标值
 */
int Point::getCol() const {
    return col_;
}

/**
 * @brief 获取棋子类型
 * 
 * @return 返回该位置的棋子类型
 */
PieceType Point::getPiece() const {
    return piece_;
}

/**
 * @brief 设置行坐标
 * 
 * @param row 新的行坐标值
 * 
 * 这个方法允许修改Point对象的行坐标。
 * 在实际使用中，我们可能需要添加边界检查。
 */
void Point::setRow(int row) {
    row_ = row;
}

/**
 * @brief 设置列坐标
 * 
 * @param col 新的列坐标值
 */
void Point::setCol(int col) {
    col_ = col;
}

/**
 * @brief 设置棋子类型
 * 
 * @param piece 新的棋子类型
 * 
 * 这个方法用于在棋盘位置放置或移除棋子。
 */
void Point::setPiece(PieceType piece) {
    piece_ = piece;
}

/**
 * @brief 检查坐标是否有效
 * 
 * @return 如果坐标在有效范围内返回true
 * 
 * 五子棋棋盘是15x15，所以有效坐标范围是0-14。
 * 这个方法用于验证用户输入或程序计算的坐标是否合法。
 */
bool Point::isValid() const {
    return (row_ >= 0 && row_ < BOARD_SIZE && 
            col_ >= 0 && col_ < BOARD_SIZE);
}

/**
 * @brief 检查该位置是否为空
 * 
 * @return 如果该位置没有棋子返回true
 * 
 * 这个方法用于判断某个位置是否可以下棋。
 * 只有空位置才能放置新的棋子。
 */
bool Point::isEmpty() const {
    return piece_ == PieceType::EMPTY;
}

/**
 * @brief 等于运算符重载
 * 
 * @param other 要比较的Point对象
 * @return 如果两个点的坐标相同返回true
 * 
 * 注意：这里只比较坐标，不比较棋子类型。
 * 因为我们通常关心的是位置是否相同，而不是棋子是否相同。
 */
bool Point::operator==(const Point& other) const {
    return (row_ == other.row_ && col_ == other.col_);
}

/**
 * @brief 不等于运算符重载
 * 
 * @param other 要比较的Point对象
 * @return 如果两个点的坐标不同返回true
 * 
 * 通常实现为等于运算符的取反，保持逻辑一致性。
 */
bool Point::operator!=(const Point& other) const {
    return !(*this == other);  // 使用已定义的==运算符
}
