#define _CRT_SECURE_NO_WARNINGS
#include <windows.h>
#include <windowsx.h>
#include <commctrl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "global.h"
#include "game.h"
#include "modal.h"
#include "rank_service.h"

#pragma comment(lib, "comctl32.lib")

// 窗口和控件ID定义
#define ID_NEW_GAME     1001
#define ID_RESTART      1002
#define ID_RANK_LIST    1003
#define ID_GAME_RULE    1004
#define ID_ABOUT        1005
#define ID_EXIT         1006

// 全局变量
HINSTANCE g_hInst;
HWND g_hWnd;
HWND g_hStatusBar;
int g_currentPlayer = 0;  // 0=黑子, 1=白子
int g_gameState = JUDGE_PLAY;
int g_stepCount = 0;

// 棋盘绘制参数
#define BOARD_SIZE 15
#define CELL_SIZE 30
#define BOARD_MARGIN 50
#define STONE_RADIUS 12

// 函数声明
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void DrawBoard(HDC hdc);
void DrawStone(HDC hdc, int row, int col, int color);
void HandleMouseClick(int x, int y);
void UpdateStatusBar();
void ShowGameResult(int result, int winner);
void CreateMenuBar(HWND hwnd);
void ShowRankDialog();
void ShowRuleDialog();
void ShowAboutDialog();
void NewGame();

// 程序入口点
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    g_hInst = hInstance;
    
    // 注册窗口类
    WNDCLASSEX wc = {0};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hIcon = LoadIcon(NULL, IDI_APPLICATION);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = L"GomokuGame";
    wc.hIconSm = LoadIcon(NULL, IDI_APPLICATION);
    
    if (!RegisterClassEx(&wc))
    {
        MessageBox(NULL, L"窗口注册失败!", L"错误", MB_ICONERROR);
        return 0;
    }
    
    // 创建主窗口
    int windowWidth = BOARD_SIZE * CELL_SIZE + BOARD_MARGIN * 2 + 20;
    int windowHeight = BOARD_SIZE * CELL_SIZE + BOARD_MARGIN * 2 + 100;
    
    g_hWnd = CreateWindowEx(
        0,
        L"GomokuGame",
        L"五子棋游戏",
        WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX & ~WS_THICKFRAME,
        CW_USEDEFAULT, CW_USEDEFAULT,
        windowWidth, windowHeight,
        NULL, NULL, hInstance, NULL
    );
    
    if (!g_hWnd)
    {
        MessageBox(NULL, L"窗口创建失败!", L"错误", MB_ICONERROR);
        return 0;
    }
    
    // 创建菜单
    CreateMenuBar(g_hWnd);
    
    // 创建状态栏
    g_hStatusBar = CreateWindowEx(
        0, STATUSCLASSNAME, NULL,
        WS_CHILD | WS_VISIBLE | SBARS_SIZEGRIP,
        0, 0, 0, 0,
        g_hWnd, NULL, hInstance, NULL
    );
    
    // 初始化游戏
    initRanks();
    NewGame();
    
    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);
    
    // 消息循环
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // 清理资源
    saveRanks();
    clearRanks();
    
    return (int)msg.wParam;
}

// 窗口过程函数
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_CREATE:
        return 0;
        
    case WM_PAINT:
        {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            DrawBoard(hdc);
            EndPaint(hwnd, &ps);
        }
        return 0;
        
    case WM_LBUTTONDOWN:
        {
            int x = GET_X_LPARAM(lParam);
            int y = GET_Y_LPARAM(lParam);
            HandleMouseClick(x, y);
        }
        return 0;
        
    case WM_COMMAND:
        switch (LOWORD(wParam))
        {
        case ID_NEW_GAME:
            NewGame();
            break;
        case ID_RESTART:
            NewGame();
            break;
        case ID_RANK_LIST:
            ShowRankDialog();
            break;
        case ID_GAME_RULE:
            ShowRuleDialog();
            break;
        case ID_ABOUT:
            ShowAboutDialog();
            break;
        case ID_EXIT:
            PostQuitMessage(0);
            break;
        }
        return 0;
        
    case WM_SIZE:
        // 调整状态栏大小
        if (g_hStatusBar)
        {
            SendMessage(g_hStatusBar, WM_SIZE, 0, 0);
        }
        return 0;
        
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

// 创建菜单栏
void CreateMenuBar(HWND hwnd)
{
    HMENU hMenuBar = CreateMenu();
    HMENU hGameMenu = CreatePopupMenu();
    HMENU hHelpMenu = CreatePopupMenu();
    
    // 游戏菜单
    AppendMenu(hGameMenu, MF_STRING, ID_NEW_GAME, L"新游戏(&N)");
    AppendMenu(hGameMenu, MF_STRING, ID_RESTART, L"重新开始(&R)");
    AppendMenu(hGameMenu, MF_SEPARATOR, 0, NULL);
    AppendMenu(hGameMenu, MF_STRING, ID_RANK_LIST, L"排行榜(&L)");
    AppendMenu(hGameMenu, MF_SEPARATOR, 0, NULL);
    AppendMenu(hGameMenu, MF_STRING, ID_EXIT, L"退出(&X)");
    
    // 帮助菜单
    AppendMenu(hHelpMenu, MF_STRING, ID_GAME_RULE, L"游戏规则(&R)");
    AppendMenu(hHelpMenu, MF_STRING, ID_ABOUT, L"关于(&A)");
    
    // 添加到菜单栏
    AppendMenu(hMenuBar, MF_POPUP, (UINT_PTR)hGameMenu, L"游戏(&G)");
    AppendMenu(hMenuBar, MF_POPUP, (UINT_PTR)hHelpMenu, L"帮助(&H)");
    
    SetMenu(hwnd, hMenuBar);
}

// 开始新游戏
void NewGame()
{
    initStatus();
    g_currentPlayer = 0;
    g_gameState = JUDGE_PLAY;
    g_stepCount = 0;
    UpdateStatusBar();
    InvalidateRect(g_hWnd, NULL, TRUE);
}

// 更新状态栏
void UpdateStatusBar()
{
    wchar_t statusText[100];
    if (g_gameState == JUDGE_PLAY)
    {
        if (g_currentPlayer == 0)
            wcscpy_s(statusText, 100, L"当前玩家: 黑子");
        else
            wcscpy_s(statusText, 100, L"当前玩家: 白子");
    }
    else if (g_gameState == JUDGE_WIN)
    {
        if (g_currentPlayer == 0)
            wcscpy_s(statusText, 100, L"游戏结束 - 黑子获胜!");
        else
            wcscpy_s(statusText, 100, L"游戏结束 - 白子获胜!");
    }
    else if (g_gameState == JUDGE_DRAW)
    {
        wcscpy_s(statusText, 100, L"游戏结束 - 平局!");
    }
    
    SendMessage(g_hStatusBar, SB_SETTEXT, 0, (LPARAM)statusText);
}

// 绘制棋盘
void DrawBoard(HDC hdc)
{
    // 设置背景色
    RECT clientRect;
    GetClientRect(g_hWnd, &clientRect);
    FillRect(hdc, &clientRect, (HBRUSH)(COLOR_WINDOW + 1));

    // 绘制棋盘网格
    HPEN hPen = CreatePen(PS_SOLID, 1, RGB(0, 0, 0));
    HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);

    // 绘制垂直线
    for (int i = 0; i < BOARD_SIZE; i++)
    {
        int x = BOARD_MARGIN + i * CELL_SIZE;
        MoveToEx(hdc, x, BOARD_MARGIN, NULL);
        LineTo(hdc, x, BOARD_MARGIN + (BOARD_SIZE - 1) * CELL_SIZE);
    }

    // 绘制水平线
    for (int i = 0; i < BOARD_SIZE; i++)
    {
        int y = BOARD_MARGIN + i * CELL_SIZE;
        MoveToEx(hdc, BOARD_MARGIN, y, NULL);
        LineTo(hdc, BOARD_MARGIN + (BOARD_SIZE - 1) * CELL_SIZE, y);
    }

    // 绘制天元点和星位
    HBRUSH hBrush = CreateSolidBrush(RGB(0, 0, 0));
    HBRUSH hOldBrush = (HBRUSH)SelectObject(hdc, hBrush);

    // 天元点 (7,7)
    int centerX = BOARD_MARGIN + 7 * CELL_SIZE;
    int centerY = BOARD_MARGIN + 7 * CELL_SIZE;
    Ellipse(hdc, centerX - 3, centerY - 3, centerX + 3, centerY + 3);

    // 四个星位
    int starPositions[][2] = {{3, 3}, {3, 11}, {11, 3}, {11, 11}};
    for (int i = 0; i < 4; i++)
    {
        int x = BOARD_MARGIN + starPositions[i][0] * CELL_SIZE;
        int y = BOARD_MARGIN + starPositions[i][1] * CELL_SIZE;
        Ellipse(hdc, x - 2, y - 2, x + 2, y + 2);
    }

    SelectObject(hdc, hOldBrush);
    DeleteObject(hBrush);

    // 绘制棋子
    for (int row = 0; row < BOARD_SIZE; row++)
    {
        for (int col = 0; col < BOARD_SIZE; col++)
        {
            Point point = {row, col, 0};
            int status = getStatus(point);
            if (status != STATUS_BLANK)
            {
                DrawStone(hdc, row, col, status);
            }
        }
    }

    SelectObject(hdc, hOldPen);
    DeleteObject(hPen);
}

// 绘制棋子
void DrawStone(HDC hdc, int row, int col, int color)
{
    int x = BOARD_MARGIN + col * CELL_SIZE;
    int y = BOARD_MARGIN + row * CELL_SIZE;

    HBRUSH hBrush;
    if (color == STATUS_BLACK)
        hBrush = CreateSolidBrush(RGB(0, 0, 0));
    else
        hBrush = CreateSolidBrush(RGB(255, 255, 255));

    HBRUSH hOldBrush = (HBRUSH)SelectObject(hdc, hBrush);

    // 绘制棋子边框
    HPEN hPen = CreatePen(PS_SOLID, 1, RGB(0, 0, 0));
    HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);

    Ellipse(hdc, x - STONE_RADIUS, y - STONE_RADIUS,
            x + STONE_RADIUS, y + STONE_RADIUS);

    SelectObject(hdc, hOldPen);
    SelectObject(hdc, hOldBrush);
    DeleteObject(hPen);
    DeleteObject(hBrush);
}

// 处理鼠标点击
void HandleMouseClick(int x, int y)
{
    if (g_gameState != JUDGE_PLAY)
        return;

    // 计算点击的棋盘位置
    int col = (x - BOARD_MARGIN + CELL_SIZE / 2) / CELL_SIZE;
    int row = (y - BOARD_MARGIN + CELL_SIZE / 2) / CELL_SIZE;

    // 检查是否在棋盘范围内
    if (row < 0 || row >= BOARD_SIZE || col < 0 || col >= BOARD_SIZE)
        return;

    // 检查该位置是否已有棋子
    Point point = {row, col, g_currentPlayer};
    if (getStatus(point) != STATUS_BLANK)
        return;

    // 下棋
    setStatus(point);
    g_stepCount++;

    // 判断游戏结果
    g_gameState = judge(point);

    if (g_gameState == JUDGE_WIN)
    {
        ShowGameResult(g_gameState, g_currentPlayer);
        // 检查是否进入排行榜
        int order = judgeOrder(g_stepCount);
        if (order <= 10)
        {
            wchar_t message[200];
            swprintf_s(message, 200, L"恭喜获胜！用了%d步，进入前十名排行榜第%d位！", g_stepCount, order);
            MessageBox(g_hWnd, message, L"进入排行榜", MB_OK | MB_ICONINFORMATION);

            // 简化处理，使用默认玩家名
            Rank rank;
            strcpy_s(rank.name, 30, "Player");
            rank.step = g_stepCount;
            inserRank(order - 1, rank);
        }
    }
    else if (g_gameState == JUDGE_DRAW)
    {
        ShowGameResult(g_gameState, -1);
    }
    else
    {
        // 切换玩家
        g_currentPlayer = 1 - g_currentPlayer;
    }

    UpdateStatusBar();
    InvalidateRect(g_hWnd, NULL, TRUE);
}

// 显示游戏结果
void ShowGameResult(int result, int winner)
{
    wchar_t message[100];
    if (result == JUDGE_WIN)
    {
        if (winner == STATUS_BLACK)
            wcscpy_s(message, 100, L"黑子获胜！");
        else
            wcscpy_s(message, 100, L"白子获胜！");
    }
    else if (result == JUDGE_DRAW)
    {
        wcscpy_s(message, 100, L"平局！");
    }

    MessageBox(g_hWnd, message, L"游戏结束", MB_OK | MB_ICONINFORMATION);
}

// 显示排行榜对话框
void ShowRankDialog()
{
    const int maxSize = getRankSize();
    Rank* ranks = (Rank*)malloc(sizeof(Rank) * maxSize);
    memset(ranks, 0, sizeof(Rank) * maxSize);

    int size = getRanks(ranks, maxSize);

    wchar_t rankText[2000] = L"排行榜\n\n排名\t姓名\t步数\n";
    wchar_t line[100];

    for (int i = 0; i < maxSize; i++)
    {
        wchar_t name[30];
        MultiByteToWideChar(CP_ACP, 0, ranks[i].name, -1, name, 30);
        swprintf_s(line, 100, L"%d\t%s\t%d\n", i + 1, name, ranks[i].step);
        wcscat_s(rankText, 2000, line);
    }

    free(ranks);
    MessageBox(g_hWnd, rankText, L"排行榜", MB_OK | MB_ICONINFORMATION);
}

// 显示游戏规则对话框
void ShowRuleDialog()
{
    wchar_t ruleText[] = L"游戏规则\n\n"
                        L"1. 游戏双方分别使用黑白两色棋子\n"
                        L"2. 黑子先行，白子后行\n"
                        L"3. 棋盘：15×15\n"
                        L"4. 同色棋子在横、竖、斜任一方向连成5子为赢\n"
                        L"5. 双方轮流下子，直到分出胜负或平局\n"
                        L"6. 点击棋盘交叉点下子\n"
                        L"7. 黑子先下\n"
                        L"8. 选择开始游戏即可开始对弈\n";

    MessageBox(g_hWnd, ruleText, L"游戏规则", MB_OK | MB_ICONINFORMATION);
}

// 显示关于对话框
void ShowAboutDialog()
{
    wchar_t aboutText[] = L"五子棋游戏\n\n"
                         L"版本：Windows GUI版\n"
                         L"基于C语言开发\n\n"
                         L"享受游戏乐趣！";

    MessageBox(g_hWnd, aboutText, L"关于", MB_OK | MB_ICONINFORMATION);
}
