# 五子棋游戏 - C++版本

## 项目简介

这是一个使用C++和Win32 API开发的五子棋游戏，具有完整的图形用户界面。项目采用面向对象设计，代码结构清晰，注释详细，适合作为C++编程学习的参考项目。

## 功能特性

### 🎮 游戏功能
- ✅ 标准15×15五子棋规则
- ✅ 黑白双方轮流下棋
- ✅ 实时胜负判断（横、竖、斜四个方向）
- ✅ 平局检测
- ✅ 游戏重新开始

### 🖥️ 界面功能
- ✅ 原生Windows GUI界面
- ✅ 鼠标点击下棋
- ✅ 棋盘网格和标记点显示
- ✅ 最后一步棋高亮显示
- ✅ 状态栏信息显示
- ✅ 菜单栏操作

### 📊 数据功能
- ✅ 排行榜系统
- ✅ 成绩按步数排序
- ✅ 数据文件持久化存储
- ✅ 获胜记录自动保存

### ⌨️ 快捷键支持
- `F1` - 显示游戏规则
- `F2` - 开始新游戏
- `F5` - 重新开始
- `Alt+F4` - 退出游戏

## 技术栈

- **编程语言**: C++17
- **开发环境**: Visual Studio 2019/2022
- **界面框架**: Win32 API
- **设计模式**: MVC (Model-View-Controller)
- **内存管理**: 智能指针 (std::unique_ptr)
- **构建系统**: MSBuild / CMake

## 项目结构

```
GomokuCpp/
├── src/                    # 源代码目录
│   ├── main.cpp           # 程序入口点
│   ├── GomokuApp.h/cpp    # 主应用程序类 (View层)
│   ├── GameController.h/cpp # 游戏控制器 (Controller层)
│   ├── GameBoard.h/cpp    # 游戏棋盘逻辑 (Model层)
│   ├── RankManager.h/cpp  # 排行榜管理 (Model层)
│   └── Point.h/cpp        # 基础数据结构
├── bin/                   # 编译输出目录
├── obj/                   # 中间文件目录
├── GomokuCpp.sln          # Visual Studio解决方案
├── GomokuCpp.vcxproj      # Visual Studio项目文件
├── CMakeLists.txt         # CMake构建文件
├── build.bat              # 编译脚本
├── 学生文档.md            # 详细学习文档
└── README.md              # 本文件
```

## 快速开始

### 方法1: Visual Studio (推荐)

1. **打开项目**
   ```
   双击 GomokuCpp.sln 文件
   ```

2. **编译运行**
   - 选择 `Release` 或 `Debug` 配置
   - 按 `F5` 编译并运行
   - 或按 `Ctrl+F5` 编译并运行（不调试）

### 方法2: 编译脚本

1. **运行编译脚本**
   ```bash
   双击 build.bat 文件
   ```

2. **手动运行**
   ```bash
   cd bin\Win32\Release
   GomokuGame.exe
   ```

### 方法3: CMake

1. **生成项目文件**
   ```bash
   mkdir build
   cd build
   cmake .. -G "Visual Studio 16 2019" -A Win32
   ```

2. **编译**
   ```bash
   cmake --build . --config Release
   ```

### 方法4: 命令行编译

```bash
# 设置Visual Studio环境
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat"

# 编译
cl /EHsc /std:c++17 /Fe:GomokuGame.exe src/*.cpp user32.lib gdi32.lib comctl32.lib
```

## 系统要求

### 开发环境
- Windows 10 或更高版本
- Visual Studio 2019 或 2022
- Windows 10 SDK
- C++ 桌面开发工作负载

### 运行环境
- Windows 7 或更高版本
- 至少 50MB 可用磁盘空间
- 支持 Win32 应用程序

## 游戏规则

### 基本规则
1. 游戏在15×15的棋盘上进行
2. 黑子先行，白子后行
3. 双方轮流在棋盘交叉点上下子
4. 棋子下定后不能移动

### 获胜条件
率先在以下任一方向连成5个同色棋子者获胜：
- 水平方向（横向）
- 垂直方向（纵向）  
- 主对角线方向（\）
- 副对角线方向（/）

### 平局条件
棋盘下满（225手）仍无人获胜则为平局。

## 核心算法

### 五子连珠判断
```cpp
bool GameBoard::checkHorizontal(const Point& point) const {
    int count = 1;  // 包括当前位置
    PieceType piece = point.getPiece();
    
    // 向左检查 + 向右检查
    // 计算连续相同棋子数量
    
    return count >= 5;  // 五子连珠
}
```

### 坐标转换
```cpp
bool GomokuApp::ScreenToBoardCoords(int x, int y, int& row, int& col) {
    // 屏幕坐标转换为棋盘坐标
    // 使用四舍五入实现就近对齐
    col = (relativeX + CELL_SIZE / 2) / CELL_SIZE;
    row = (relativeY + CELL_SIZE / 2) / CELL_SIZE;
    
    return (row >= 0 && row < BOARD_SIZE && col >= 0 && col < BOARD_SIZE);
}
```

## 设计模式

### MVC模式
- **Model**: `GameBoard`, `RankManager` - 数据和业务逻辑
- **View**: `GomokuApp` - 用户界面和显示
- **Controller**: `GameController` - 控制逻辑和协调

### 单例模式
```cpp
GameController& GameController::getInstance() {
    static GameController instance;  // 线程安全的单例
    return instance;
}
```

### RAII原则
```cpp
std::unique_ptr<GameBoard> gameBoard_;  // 自动内存管理
```

## 学习价值

这个项目适合学习以下内容：

### C++核心概念
- 面向对象编程（封装、继承、多态）
- STL容器和算法
- 智能指针和内存管理
- 异常处理机制
- const正确性

### Windows编程
- Win32 API基础
- 事件驱动编程
- 图形绘制和GDI
- 消息处理机制

### 软件设计
- MVC设计模式
- 单例模式
- 接口设计
- 模块化编程

### 算法设计
- 游戏逻辑算法
- 数据排序算法
- 坐标转换算法

## 扩展建议

### 初级扩展
- [ ] 添加悔棋功能
- [ ] 添加游戏计时器
- [ ] 美化界面样式
- [ ] 添加音效支持

### 中级扩展
- [ ] 实现简单AI对手
- [ ] 添加游戏回放功能
- [ ] 支持自定义棋盘大小
- [ ] 添加游戏存档功能

### 高级扩展
- [ ] 实现高级AI算法（Alpha-Beta剪枝）
- [ ] 网络对战功能
- [ ] 实现禁手规则
- [ ] 制作安装包

## 故障排除

### 编译问题

**Q: 编译时出现链接错误？**
A: 确保链接了必要的Windows库：
```
user32.lib gdi32.lib comctl32.lib kernel32.lib
```

**Q: 找不到头文件？**
A: 检查包含目录设置，确保 `src` 目录在包含路径中。

### 运行问题

**Q: 程序启动时崩溃？**
A: 检查以下几点：
- 确保所有依赖库都已链接
- 检查指针初始化
- 确认文件权限

**Q: 中文显示乱码？**
A: 确保项目设置为Unicode字符集，使用宽字符串。

### 调试技巧

1. 使用Visual Studio调试器
2. 设置断点进行单步调试
3. 查看变量值和调用堆栈
4. 使用输出窗口查看调试信息

## 贡献指南

欢迎提交问题报告和改进建议！

1. Fork 本项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目仅用于教育和学习目的。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues页面
- 邮箱：[学生邮箱]

---

**享受编程的乐趣！** 🎮✨
