@echo off
echo 正在启动Visual Studio 2019打开五子棋GUI项目...

REM 尝试不同的Visual Studio 2019安装路径
set VS_PATH=""

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe" (
    set VS_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe" (
    set VS_PATH="C:\Program Files\Microsoft Visual Studio\2019\Community\Common7\IDE\devenv.exe"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\devenv.exe" (
    set VS_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\IDE\devenv.exe"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Professional\Common7\IDE\devenv.exe" (
    set VS_PATH="C:\Program Files\Microsoft Visual Studio\2019\Professional\Common7\IDE\devenv.exe"
)

if %VS_PATH%=="" (
    echo 错误: 找不到Visual Studio 2019
    echo 请手动双击 Game.sln 文件打开项目
    pause
    exit /b 1
)

echo 找到Visual Studio: %VS_PATH%
echo 正在打开项目...

%VS_PATH% Game.sln

echo Visual Studio已启动
echo 在Visual Studio中按F5编译并运行项目
pause
