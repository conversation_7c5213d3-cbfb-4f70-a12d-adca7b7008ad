/**
 * @file GameController.h
 * @brief 五子棋游戏控制器类的头文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件定义了GameController类，它是整个五子棋游戏的控制中心。
 * GameController采用MVC（Model-View-Controller）设计模式中的Controller角色，
 * 负责协调游戏逻辑（Model）和用户界面（View）之间的交互。
 * 
 * 主要职责：
 * - 管理游戏流程
 * - 处理用户输入
 * - 更新游戏状态
 * - 管理排行榜
 * - 提供游戏接口
 */

#ifndef GAMECONTROLLER_H
#define GAMECONTROLLER_H

#include "GameBoard.h"
#include "RankManager.h"
#include "Point.h"
#include <string>
#include <memory>

/**
 * @brief 游戏状态枚举
 * 
 * 定义游戏的不同状态，用于控制游戏流程。
 */
enum class GameState {
    MENU,        // 主菜单状态
    PLAYING,     // 游戏进行中
    GAME_OVER,   // 游戏结束
    SHOW_RANK,   // 显示排行榜
    SHOW_RULES,  // 显示游戏规则
    EXIT         // 退出游戏
};

/**
 * @brief 游戏控制器类
 * 
 * 这个类是整个五子棋游戏的核心控制器，采用单例模式设计。
 * 它协调各个组件之间的交互，管理游戏的整体流程。
 * 
 * 设计特点：
 * 1. 单例模式：确保全局只有一个游戏控制器实例
 * 2. 状态机：使用状态枚举管理游戏流程
 * 3. 组合模式：包含GameBoard和RankManager对象
 * 4. 接口分离：为不同的UI提供统一的接口
 * 
 * 使用示例：
 * GameController& game = GameController::getInstance();
 * game.startNewGame();
 * game.makeMove(7, 7);
 */
class GameController {
private:
    // 私有成员变量
    std::unique_ptr<GameBoard> gameBoard_;    // 游戏棋盘（智能指针管理内存）
    std::unique_ptr<RankManager> rankManager_; // 排行榜管理器
    GameState currentState_;                   // 当前游戏状态
    std::string lastMessage_;                  // 最后的消息（用于UI显示）
    bool gameInitialized_;                     // 游戏是否已初始化
    
    // 私有构造函数（单例模式）
    /**
     * @brief 私有构造函数
     * 
     * 单例模式的实现，防止外部直接创建对象。
     */
    GameController();
    
    /**
     * @brief 私有析构函数
     */
    ~GameController();
    
    // 禁用拷贝构造和赋值操作（单例模式）
    GameController(const GameController&) = delete;
    GameController& operator=(const GameController&) = delete;
    
    /**
     * @brief 初始化游戏组件
     * 
     * 创建游戏棋盘和排行榜管理器，加载排行榜数据。
     */
    void initializeGame();
    
    /**
     * @brief 处理游戏结束逻辑
     * @param result 游戏结果
     * 
     * 当游戏结束时调用，处理获胜、平局等情况，
     * 并检查是否需要更新排行榜。
     */
    void handleGameEnd(GameResult result);

public:
    /**
     * @brief 获取单例实例
     * @return 返回GameController的唯一实例
     * 
     * 这是单例模式的标准实现方法。
     * 使用局部静态变量确保线程安全和延迟初始化。
     */
    static GameController& getInstance();
    
    /**
     * @brief 初始化游戏系统
     * @param rankFilename 排行榜文件名
     * @return 如果初始化成功返回true
     * 
     * 这个方法必须在使用其他功能之前调用。
     */
    bool initialize(const std::string& rankFilename = "ranklist.dat");
    
    /**
     * @brief 开始新游戏
     * 
     * 重置游戏棋盘，设置游戏状态为进行中。
     */
    void startNewGame();
    
    /**
     * @brief 在指定位置下棋
     * @param row 行坐标 (0-14)
     * @param col 列坐标 (0-14)
     * @return 如果下棋成功返回true
     * 
     * 这是游戏的核心交互方法，处理玩家的下棋操作。
     */
    bool makeMove(int row, int col);
    
    /**
     * @brief 在指定位置下棋（Point版本）
     * @param point 下棋位置
     * @return 如果下棋成功返回true
     */
    bool makeMove(const Point& point);
    
    /**
     * @brief 检查指定位置是否可以下棋
     * @param row 行坐标
     * @param col 列坐标
     * @return 如果可以下棋返回true
     */
    bool isValidMove(int row, int col) const;
    
    /**
     * @brief 获取当前游戏状态
     * @return 返回当前游戏状态枚举值
     */
    GameState getCurrentState() const;
    
    /**
     * @brief 获取游戏结果
     * @return 返回当前游戏结果
     */
    GameResult getGameResult() const;
    
    /**
     * @brief 获取当前玩家
     * @return 返回当前应该下棋的玩家
     */
    PieceType getCurrentPlayer() const;
    
    /**
     * @brief 获取指定位置的棋子类型
     * @param row 行坐标
     * @param col 列坐标
     * @return 返回该位置的棋子类型
     */
    PieceType getPiece(int row, int col) const;
    
    /**
     * @brief 获取总步数
     * @return 返回已下棋子的总数
     */
    int getTotalMoves() const;
    
    /**
     * @brief 获取最后一步棋的位置
     * @return 返回最后下棋的位置
     */
    Point getLastMove() const;
    
    /**
     * @brief 获取棋盘的字符串表示
     * @return 返回棋盘的字符串表示
     */
    std::string getBoardString() const;
    
    /**
     * @brief 添加排行榜记录
     * @param playerName 玩家姓名
     * @param steps 获胜步数
     * @return 如果添加成功返回true
     */
    bool addRankRecord(const std::string& playerName, int steps);
    
    /**
     * @brief 检查指定步数是否能进入排行榜
     * @param steps 要检查的步数
     * @return 如果能进入排行榜返回true
     */
    bool canEnterRank(int steps) const;
    
    /**
     * @brief 获取指定步数的排名
     * @param steps 要查询的步数
     * @return 返回排名（1-based），如果不能进入返回-1
     */
    int getRankPosition(int steps) const;
    
    /**
     * @brief 获取排行榜字符串
     * @return 返回格式化的排行榜字符串
     */
    std::string getRankString() const;
    
    /**
     * @brief 获取排行榜记录数量
     * @return 返回当前记录数量
     */
    int getRankCount() const;
    
    /**
     * @brief 获取指定位置的排行榜记录
     * @param index 记录索引 (0-based)
     * @return 返回记录的引用
     */
    const RankRecord& getRankRecord(int index) const;
    
    /**
     * @brief 保存排行榜数据
     * @return 如果保存成功返回true
     */
    bool saveRankData();
    
    /**
     * @brief 加载排行榜数据
     * @return 如果加载成功返回true
     */
    bool loadRankData();
    
    /**
     * @brief 设置游戏状态
     * @param state 新的游戏状态
     */
    void setState(GameState state);
    
    /**
     * @brief 获取最后的消息
     * @return 返回最后的消息字符串
     */
    std::string getLastMessage() const;
    
    /**
     * @brief 设置消息
     * @param message 要设置的消息
     */
    void setMessage(const std::string& message);
    
    /**
     * @brief 获取游戏规则字符串
     * @return 返回游戏规则的详细说明
     */
    std::string getGameRules() const;
    
    /**
     * @brief 重置游戏
     * 
     * 重置游戏状态，但保留排行榜数据。
     */
    void resetGame();
    
    /**
     * @brief 退出游戏
     * 
     * 保存数据并设置退出状态。
     */
    void exitGame();
};

#endif // GAMECONTROLLER_H
