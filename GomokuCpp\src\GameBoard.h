/**
 * @file GameBoard.h
 * @brief 五子棋游戏板类的头文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件定义了GameBoard类，它是五子棋游戏的核心类。
 * GameBoard类负责：
 * - 管理15x15的棋盘状态
 * - 处理下棋操作
 * - 判断游戏胜负
 * - 提供棋盘显示功能
 */

#ifndef GAMEBOARD_H
#define GAMEBOARD_H

#include "Point.h"
#include <vector>
#include <string>

/**
 * @brief 游戏板类
 * 
 * 这个类封装了五子棋游戏的所有核心逻辑：
 * - 棋盘状态管理
 * - 下棋规则检查
 * - 胜负判断算法
 * - 棋盘显示
 * 
 * 设计思路：
 * 1. 使用二维vector存储棋盘状态，比C语言的数组更安全
 * 2. 将游戏逻辑封装在类中，提高代码的可维护性
 * 3. 提供清晰的公共接口，隐藏内部实现细节
 */
class GameBoard {
private:
    // 私有成员变量
    static const int BOARD_SIZE = 15;  // 棋盘大小常量
    
    /**
     * @brief 棋盘状态存储
     * 
     * 使用二维vector存储棋盘状态。
     * vector相比数组的优势：
     * - 自动内存管理，不需要手动分配释放内存
     * - 边界检查，更安全
     * - 提供丰富的成员函数
     */
    std::vector<std::vector<PieceType>> board_;
    
    PieceType currentPlayer_;  // 当前玩家 (BLACK或WHITE)
    int totalMoves_;          // 总步数计数器
    Point lastMove_;          // 最后一步棋的位置
    
    // 私有辅助方法
    
    /**
     * @brief 检查水平方向是否有五子连珠
     * @param point 要检查的位置
     * @return 如果有五子连珠返回true
     */
    bool checkHorizontal(const Point& point) const;
    
    /**
     * @brief 检查垂直方向是否有五子连珠
     * @param point 要检查的位置
     * @return 如果有五子连珠返回true
     */
    bool checkVertical(const Point& point) const;
    
    /**
     * @brief 检查主对角线方向是否有五子连珠
     * @param point 要检查的位置
     * @return 如果有五子连珠返回true
     */
    bool checkMainDiagonal(const Point& point) const;
    
    /**
     * @brief 检查副对角线方向是否有五子连珠
     * @param point 要检查的位置
     * @return 如果有五子连珠返回true
     */
    bool checkAntiDiagonal(const Point& point) const;
    
    /**
     * @brief 在指定方向计算连续相同棋子的数量
     * @param point 起始位置
     * @param deltaRow 行方向增量 (-1, 0, 1)
     * @param deltaCol 列方向增量 (-1, 0, 1)
     * @return 返回连续相同棋子的数量
     */
    int countDirection(const Point& point, int deltaRow, int deltaCol) const;

public:
    /**
     * @brief 默认构造函数
     * 
     * 创建一个新的游戏板，初始化为空棋盘，黑子先行。
     */
    GameBoard();
    
    /**
     * @brief 析构函数
     * 
     * 清理资源。由于使用了vector，通常不需要手动清理，
     * 但保留析构函数是好的编程习惯。
     */
    ~GameBoard();
    
    /**
     * @brief 重置游戏板
     * 
     * 将棋盘重置为初始状态：
     * - 清空所有棋子
     * - 重置为黑子先行
     * - 步数归零
     */
    void reset();
    
    /**
     * @brief 在指定位置下棋
     * @param row 行坐标 (0-14)
     * @param col 列坐标 (0-14)
     * @return 如果下棋成功返回true，否则返回false
     * 
     * 下棋失败的情况：
     * - 坐标超出棋盘范围
     * - 该位置已有棋子
     * - 游戏已结束
     */
    bool makeMove(int row, int col);
    
    /**
     * @brief 在指定位置下棋（Point版本）
     * @param point 要下棋的位置
     * @return 如果下棋成功返回true，否则返回false
     */
    bool makeMove(const Point& point);
    
    /**
     * @brief 检查游戏是否结束并返回结果
     * @return 返回游戏结果枚举值
     * 
     * 可能的返回值：
     * - GameResult::CONTINUE: 游戏继续
     * - GameResult::BLACK_WIN: 黑子获胜
     * - GameResult::WHITE_WIN: 白子获胜
     * - GameResult::DRAW: 平局（棋盘满了但没有获胜者）
     */
    GameResult checkGameResult() const;
    
    /**
     * @brief 获取指定位置的棋子类型
     * @param row 行坐标
     * @param col 列坐标
     * @return 返回该位置的棋子类型
     */
    PieceType getPiece(int row, int col) const;
    
    /**
     * @brief 获取当前玩家
     * @return 返回当前应该下棋的玩家
     */
    PieceType getCurrentPlayer() const;
    
    /**
     * @brief 获取总步数
     * @return 返回已下棋子的总数
     */
    int getTotalMoves() const;
    
    /**
     * @brief 获取最后一步棋的位置
     * @return 返回最后下棋的位置
     */
    Point getLastMove() const;
    
    /**
     * @brief 检查棋盘是否已满
     * @return 如果棋盘已满返回true
     */
    bool isFull() const;
    
    /**
     * @brief 检查指定位置是否可以下棋
     * @param row 行坐标
     * @param col 列坐标
     * @return 如果可以下棋返回true
     */
    bool isValidMove(int row, int col) const;
    
    /**
     * @brief 获取棋盘的字符串表示（用于控制台显示）
     * @return 返回棋盘的字符串表示
     * 
     * 这个方法将棋盘转换为可读的字符串格式，
     * 方便在控制台或调试时显示棋盘状态。
     */
    std::string toString() const;
    
    /**
     * @brief 获取棋盘大小
     * @return 返回棋盘大小（15）
     */
    static int getBoardSize();
};

#endif // GAMEBOARD_H
