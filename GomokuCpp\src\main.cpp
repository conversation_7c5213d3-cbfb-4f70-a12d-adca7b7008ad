/**
 * @file main.cpp
 * @brief 五子棋游戏程序的主入口文件
 * <AUTHOR>
 * @date 2024年
 * 
 * 这个文件包含了Windows GUI应用程序的主入口函数WinMain。
 * 它负责初始化应用程序并启动主消息循环。
 * 
 * 程序流程：
 * 1. WinMain函数被Windows系统调用
 * 2. 创建GomokuApp应用程序对象
 * 3. 初始化应用程序
 * 4. 运行消息循环
 * 5. 程序结束时清理资源
 */

#include "GomokuApp.h"
#include <windows.h>

/**
 * @brief Windows GUI应用程序的主入口函数
 * 
 * @param hInstance 当前应用程序实例的句柄
 * @param hPrevInstance 前一个应用程序实例的句柄（在Win32中总是NULL）
 * @param lpCmdLine 命令行参数字符串
 * @param nCmdShow 窗口显示方式（最大化、最小化、正常显示等）
 * @return 程序退出代码，0表示成功
 * 
 * WinMain是Windows GUI应用程序的标准入口点，相当于控制台程序的main函数。
 * 这个函数由Windows操作系统在程序启动时调用。
 * 
 * 参数说明：
 * - hInstance: 应用程序实例句柄，用于标识当前程序实例
 * - hPrevInstance: 在16位Windows中用于标识前一个实例，在32位系统中总是NULL
 * - lpCmdLine: 命令行参数，不包括程序名本身
 * - nCmdShow: 指定窗口如何显示（SW_SHOW, SW_HIDE, SW_MAXIMIZE等）
 */
int WINAPI WinMain(
    _In_ HINSTANCE hInstance,      // 应用程序实例句柄
    _In_opt_ HINSTANCE hPrevInstance,  // 前一个实例句柄（总是NULL）
    _In_ LPSTR lpCmdLine,          // 命令行参数
    _In_ int nCmdShow              // 窗口显示方式
) {
    // 未使用的参数，避免编译器警告
    UNREFERENCED_PARAMETER(hPrevInstance);
    UNREFERENCED_PARAMETER(lpCmdLine);
    UNREFERENCED_PARAMETER(nCmdShow);
    
    try {
        // 创建应用程序对象
        // 使用栈对象而不是动态分配，确保自动清理
        GomokuApp app;
        
        // 初始化应用程序
        if (!app.Initialize(hInstance)) {
            // 初始化失败，显示错误消息并退出
            MessageBox(nullptr, 
                      L"应用程序初始化失败！\n请检查系统环境和依赖项。", 
                      L"初始化错误", 
                      MB_OK | MB_ICONERROR);
            return -1;  // 返回错误代码
        }
        
        // 运行应用程序主循环
        // Run()方法会启动Windows消息循环，直到用户关闭程序
        int exitCode = app.Run();
        
        // 程序正常结束，返回退出代码
        return exitCode;
        
    } catch (const std::exception& e) {
        // 捕获C++标准异常
        // 将异常信息转换为宽字符串并显示
        std::string errorMsg = "程序运行时发生异常：\n";
        errorMsg += e.what();
        
        // 转换为宽字符串用于MessageBox显示
        int wideSize = MultiByteToWideChar(CP_UTF8, 0, errorMsg.c_str(), -1, nullptr, 0);
        std::wstring wideErrorMsg(wideSize, 0);
        MultiByteToWideChar(CP_UTF8, 0, errorMsg.c_str(), -1, &wideErrorMsg[0], wideSize);
        
        MessageBox(nullptr, 
                  wideErrorMsg.c_str(), 
                  L"程序异常", 
                  MB_OK | MB_ICONERROR);
        return -2;  // 返回异常错误代码
        
    } catch (...) {
        // 捕获所有其他异常
        MessageBox(nullptr, 
                  L"程序运行时发生未知异常！\n请联系开发者获取技术支持。", 
                  L"未知异常", 
                  MB_OK | MB_ICONERROR);
        return -3;  // 返回未知异常错误代码
    }
}

/**
 * @brief 程序说明和使用指南
 * 
 * 这个五子棋游戏程序的特点：
 * 
 * 【技术特点】
 * 1. 使用C++面向对象编程
 * 2. 采用Win32 API创建原生Windows界面
 * 3. 实现MVC设计模式，分离界面和逻辑
 * 4. 使用智能指针管理内存，避免内存泄漏
 * 5. 完整的异常处理机制
 * 
 * 【游戏功能】
 * 1. 标准15×15五子棋游戏
 * 2. 图形化用户界面，支持鼠标操作
 * 3. 完整的游戏逻辑和胜负判断
 * 4. 排行榜系统，记录最佳成绩
 * 5. 游戏规则说明和帮助信息
 * 
 * 【操作说明】
 * 1. 鼠标左键点击棋盘交叉点下棋
 * 2. 使用菜单栏进行游戏控制
 * 3. 支持键盘快捷键：
 *    - F1: 显示游戏规则
 *    - F2: 开始新游戏
 *    - F5: 重新开始
 * 
 * 【文件结构】
 * - main.cpp: 程序入口
 * - GomokuApp.h/cpp: 主应用程序类
 * - GameController.h/cpp: 游戏控制器
 * - GameBoard.h/cpp: 游戏棋盘逻辑
 * - RankManager.h/cpp: 排行榜管理
 * - Point.h/cpp: 基础数据结构
 * 
 * 【编译说明】
 * 1. 需要Visual Studio 2019或更高版本
 * 2. 需要Windows SDK
 * 3. 编译为Windows应用程序（不是控制台程序）
 * 4. 链接user32.lib, gdi32.lib, comctl32.lib
 * 
 * 【学习价值】
 * 这个项目适合作为C++编程学习的参考：
 * 1. 面向对象设计的实际应用
 * 2. Windows GUI编程入门
 * 3. 游戏逻辑算法实现
 * 4. 文件I/O和数据持久化
 * 5. 异常处理和错误管理
 * 6. 内存管理和资源清理
 * 
 * 【扩展建议】
 * 可以在此基础上添加更多功能：
 * 1. 人机对战（AI算法）
 * 2. 网络对战功能
 * 3. 游戏回放和棋谱保存
 * 4. 更丰富的界面效果
 * 5. 音效和动画
 * 6. 多种游戏模式
 */
