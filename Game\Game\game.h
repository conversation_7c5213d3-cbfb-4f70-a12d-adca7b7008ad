#ifndef _GAME_H_
#define _GAME_H_

#include "modal.h"

void initStatus();
int getStatus(const Point spPoint);
void setStatus(const Point spPoint);
int judge(Point spPoint);
int judgeHorizontal(const Point spPoint);
int judgeVertical(const Point spPoint);
int judgeHyperphoria(const Point spPoint);
int judgeHypophoria(const Point spPoint);
void printWinner(const int color);
int judgeDraw();
void printDraw();
#endif