@echo off
REM 五子棋游戏C++版本编译脚本
REM 这个脚本用于自动编译五子棋游戏项目

echo ========================================
echo 五子棋游戏C++版本 - 编译脚本
echo ========================================
echo.

REM 检查Visual Studio环境
echo 正在检查Visual Studio环境...

REM 尝试设置Visual Studio 2019环境变量
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat" >nul 2>&1
if errorlevel 1 (
    call "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat" >nul 2>&1
)
if errorlevel 1 (
    REM 尝试Visual Studio 2022
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat" >nul 2>&1
)
if errorlevel 1 (
    echo 错误: 找不到Visual Studio 2019/2022
    echo 请确保已安装Visual Studio并包含C++开发工具
    echo.
    echo 安装要求:
    echo - Visual Studio 2019 或 2022
    echo - C++ 桌面开发工作负载
    echo - Windows 10 SDK
    echo.
    pause
    exit /b 1
)

echo Visual Studio环境设置成功!
echo.

REM 创建输出目录
echo 创建输出目录...
if not exist "bin" mkdir bin
if not exist "bin\Win32" mkdir bin\Win32
if not exist "bin\Win32\Release" mkdir bin\Win32\Release
if not exist "obj" mkdir obj
if not exist "obj\Win32" mkdir obj\Win32
if not exist "obj\Win32\Release" mkdir obj\Win32\Release

echo 输出目录创建完成!
echo.

REM 编译源文件
echo 开始编译源文件...
echo.

REM 设置编译选项
set COMPILE_FLAGS=/EHsc /std:c++17 /W4 /O2 /DWIN32 /DNDEBUG /D_WINDOWS /DUNICODE /D_UNICODE /D_CRT_SECURE_NO_WARNINGS
set INCLUDE_DIRS=/I"src"
set LINK_FLAGS=/SUBSYSTEM:WINDOWS
set LINK_LIBS=user32.lib gdi32.lib comctl32.lib kernel32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

REM 编译各个源文件为对象文件
echo 编译 Point.cpp...
cl /c %COMPILE_FLAGS% %INCLUDE_DIRS% /Fo"obj\Win32\Release\Point.obj" "src\Point.cpp"
if errorlevel 1 goto :error

echo 编译 GameBoard.cpp...
cl /c %COMPILE_FLAGS% %INCLUDE_DIRS% /Fo"obj\Win32\Release\GameBoard.obj" "src\GameBoard.cpp"
if errorlevel 1 goto :error

echo 编译 RankManager.cpp...
cl /c %COMPILE_FLAGS% %INCLUDE_DIRS% /Fo"obj\Win32\Release\RankManager.obj" "src\RankManager.cpp"
if errorlevel 1 goto :error

echo 编译 GameController.cpp...
cl /c %COMPILE_FLAGS% %INCLUDE_DIRS% /Fo"obj\Win32\Release\GameController.obj" "src\GameController.cpp"
if errorlevel 1 goto :error

echo 编译 GomokuApp.cpp...
cl /c %COMPILE_FLAGS% %INCLUDE_DIRS% /Fo"obj\Win32\Release\GomokuApp.obj" "src\GomokuApp.cpp"
if errorlevel 1 goto :error

echo 编译 main.cpp...
cl /c %COMPILE_FLAGS% %INCLUDE_DIRS% /Fo"obj\Win32\Release\main.obj" "src\main.cpp"
if errorlevel 1 goto :error

echo.
echo 所有源文件编译完成!
echo.

REM 链接生成可执行文件
echo 正在链接生成可执行文件...
link %LINK_FLAGS% /OUT:"bin\Win32\Release\GomokuGame.exe" ^
     "obj\Win32\Release\Point.obj" ^
     "obj\Win32\Release\GameBoard.obj" ^
     "obj\Win32\Release\RankManager.obj" ^
     "obj\Win32\Release\GameController.obj" ^
     "obj\Win32\Release\GomokuApp.obj" ^
     "obj\Win32\Release\main.obj" ^
     %LINK_LIBS%

if errorlevel 1 goto :error

echo.
echo ========================================
echo 编译成功完成!
echo ========================================
echo.
echo 可执行文件位置: bin\Win32\Release\GomokuGame.exe
echo 文件大小: 
for %%A in ("bin\Win32\Release\GomokuGame.exe") do echo %%~zA 字节
echo.
echo 运行程序:
echo   cd bin\Win32\Release
echo   GomokuGame.exe
echo.
echo 或者直接双击: bin\Win32\Release\GomokuGame.exe
echo.

REM 询问是否立即运行
set /p choice="是否立即运行游戏? (y/n): "
if /i "%choice%"=="y" (
    echo 正在启动游戏...
    start "" "bin\Win32\Release\GomokuGame.exe"
)

echo 编译脚本执行完成!
pause
exit /b 0

:error
echo.
echo ========================================
echo 编译失败!
echo ========================================
echo.
echo 可能的原因:
echo 1. 源代码中有语法错误
echo 2. 缺少必要的头文件或库
echo 3. Visual Studio环境配置不正确
echo 4. 权限不足，无法写入输出目录
echo.
echo 解决建议:
echo 1. 检查源代码是否有错误
echo 2. 确保Visual Studio安装完整
echo 3. 以管理员身份运行此脚本
echo 4. 查看上面的错误信息进行调试
echo.
pause
exit /b 1
